<script setup>
import { ref } from "vue"
import { fetchUpload } from "../../request"

import iconDocx from "/static/images/file-icon-docx.svg"
import iconPdf from "/static/images/file-icon-pdf.svg"

const props = defineProps({
  attachments: {
    type: Array,
    default: () => [],
  },
})
const attachments = ref(props.attachments)
const emit = defineEmits(["update:attachments"])

const remove = (attachment) => {
  attachments.value = attachments.value.filter(
    (item) => item.id !== attachment.id
  )
  emit("update:attachments", attachments.value)
}

const upload = (file) => {
  uni.chooseMessageFile({
    count: 1,
    type: "file",
    extensions: ["docx", "pdf"],
    success: (res) => {
      const picked = res.tempFiles[0]
      const name = picked.name || "未命名"
      const ext = (name.split(".").pop() || "").toLowerCase()
      const tempId = `temp_${Date.now()}`
      const tempItem = {
        id: tempId,
        filename: name,
        ext,
        uploading: true,
        progress: 0,
      }

      attachments.value.push(tempItem)
      emit("update:attachments", attachments.value)

      let simulatedTimer = null
      let isCompleted = false

      // 模拟进度：缓慢推进到 90%
      const startSimulated = () => {
        if (simulatedTimer) return
        simulatedTimer = setInterval(() => {
          if (isCompleted) return
          const idx = attachments.value.findIndex((i) => i.id === tempId)
          if (idx === -1) return
          const current = attachments.value[idx]
          const next = Math.min(90, (current.progress || 0) + 1)
          attachments.value[idx] = { ...current, progress: next }
        }, 120)
      }

      startSimulated()

      fetchUpload(picked.path, {}, name, undefined, ({ progress }) => {
        const idx = attachments.value.findIndex((i) => i.id === tempId)
        if (idx === -1) return
        const current = attachments.value[idx]
        const next = Math.max(current.progress || 0, progress || 0)
        attachments.value[idx] = { ...current, progress: next }
      })
        .then((res1) => {
          isCompleted = true
          if (simulatedTimer) {
            clearInterval(simulatedTimer)
            simulatedTimer = null
          }
          const idx = attachments.value.findIndex((i) => i.id === tempId)
          if (idx !== -1) {
            const serverFile =
              res1.result.file || res1.file || res1.result || res1
            attachments.value.splice(idx, 1, {
              ...serverFile,
              uploading: false,
              progress: 100,
            })
            emit("update:attachments", attachments.value)
          }
        })
        .catch((err) => {
          if (simulatedTimer) {
            clearInterval(simulatedTimer)
            simulatedTimer = null
          }
          // 失败时移除临时项
          attachments.value = attachments.value.filter((i) => i.id !== tempId)
          emit("update:attachments", attachments.value)
          uni.showToast({
            title: err.message || "上传失败",
            icon: "none",
          })
        })
    },
  })
}

defineExpose({
  upload,
})
</script>

<template>
  <view class="attachments-uploader">
    <view
      class="attachments-uploader-item"
      v-for="attachment in attachments"
      :key="attachment.id"
    >
      <image
        :src="iconDocx"
        v-if="attachment.ext === 'docx'"
        class="attachments-uploader-item-icon"
      />
      <image
        :src="iconPdf"
        v-else-if="attachment.ext === 'pdf'"
        class="attachments-uploader-item-icon"
      />

      <view class="attachments-uploader-item-content">
        <view class="attachments-uploader-item-name">
          {{ attachment.filename }}
        </view>
        <view class="attachments-uploader-item-progress">
          <view
            class="ring"
            :style="{ '--p': attachment.progress || 0 }"
          ></view>
          <text class="progress-text">{{
            attachment.uploading
              ? `上传中 ${attachment.progress || 0}%`
              : `上传完成`
          }}</text>
        </view>
      </view>

      <view
        class="attachments-uploader-item-remove"
        @click="remove(attachment)"
      >
        <image src="/static/images/close.svg" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.attachments-uploader {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding-top: 16rpx;
}

.attachments-uploader-item {
  width: 412rpx;
  height: 114rpx;
  background: rgba(217, 217, 217, 0);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 2rpx solid rgba(70, 111, 255, 0.31);
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  gap: 16rpx;
  position: relative;
  overflow: hidden;

  .attachments-uploader-item-remove {
    width: 30rpx;
    height: 30rpx;
    position: absolute;
    top: 8rpx;
    right: 8rpx;

    image {
      width: 30rpx;
      height: 30rpx;
    }
  }

  .attachments-uploader-item-icon {
    width: 74rpx;
    height: 74rpx;
    flex-shrink: 0;
  }

  .attachments-uploader-item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12rpx;
    overflow: hidden;

    .attachments-uploader-item-name {
      font-size: 20rpx;
      color: #253a57;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .attachments-uploader-item-progress {
      font-size: 20rpx;
      color: #466fff;
      display: flex;
      align-items: center;
      gap: 8rpx;

      .ring {
        --size: 12px;
        --track: rgba(70, 111, 255, 0.15);
        --fill: #466fff;
        --thickness: 1px;
        width: var(--size);
        height: var(--size);
        border-radius: 50%;
        background: conic-gradient(
          var(--fill) calc(var(--p, 0) * 1%),
          var(--track) 0
        );
        -webkit-mask: radial-gradient(
          circle at 50% 50%,
          transparent 0,
          transparent calc(50% - var(--thickness) - 0.5px),
          #000 calc(50% - var(--thickness) + 0.5px),
          #000 100%
        );
        mask: radial-gradient(
          circle at 50% 50%,
          transparent 0,
          transparent calc(50% - var(--thickness) - 0.5px),
          #000 calc(50% - var(--thickness) + 0.5px),
          #000 100%
        );
        -webkit-mask-repeat: no-repeat;
        mask-repeat: no-repeat;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        position: relative;
        will-change: background;
        backface-visibility: hidden;
      }

      .progress-text {
        font-size: 20rpx;
        color: #466fff;
        line-height: 1;
      }
    }
  }
}
</style>
