<script setup>
import { ref, computed, onMounted } from "vue"

const props = defineProps({
  chatMode: {
    type: String,
    default: "assistant", // assistant | artificial
  },
  isGenerating: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits([
  "confirm",
  "exitArtificial",
  "call",
  "abort",
  "toggleMute",
  "inputBlur",
  "inputFocus",
  "click",
])

const currentAction = ref(null) // null | { title: string, icon: string, action: string }
const actions = ref([
  {
    title: "模板生成",
    icon: "/static/images/template.svg",
    action: "gen_docx",
    placeholder: "例: 金融行业劳动合同模板...",
  },
  {
    title: "内容合规",
    icon: "/static/images/check.svg",
    action: "compliance",
    placeholder: "例: 检查文档中内容，重新生成...",
  },
])

const isInputting = ref(false)
const onInputBlur = () => {
  isInputting.value = false
  emit("inputBlur")
}
const onInputFocus = () => {
  isInputting.value = true
  emit("inputFocus")
}

const inputMode = ref("text") // text | voice
const onToggleInputMode = () => {
  inputMode.value = inputMode.value === "text" ? "voice" : "text"
}

const isFullScreen = ref(false)
const toggleFullScreen = (isFull) => {
  isFullScreen.value = isFull
}

const keyboardHeight = ref("0px")
const floatBottom = computed(() => {
  return isInputting.value ? keyboardHeight.value : "0px"
})

const confirm = (text) => {
  if (text === "") {
    return
  }
  if (!currentAction.value) {
    emit("confirm", text, "text")
  } else {
    emit("confirm", text, currentAction.value.action, attachments[0])
  }
  isInputting.value = false
}

const exitArtificial = () => {
  emit("exitArtificial")
}

const call = () => {
  if (props.disabled) return
  emit("call")
}

const goLessons = () => {
  uni.navigateTo({
    url: "/pages/lessons/lessons",
  })
}

const attachmentsUploaderRef = ref(null)
const attachments = ref([])

const uploadAttachments = () => {
  attachmentsUploaderRef.value.upload()
}

onMounted(() => {
  uni.onKeyboardHeightChange((res) => {
    if (res.height > 200) {
      keyboardHeight.value = res.height + "px"
      isInputting.value = true
    } else {
      isInputting.value = false
    }
  })
})
</script>

<template>
  <view class="bottom-panel" @click.stop="emit('click')">
    <view class="abort" v-if="isGenerating" @click="emit('abort')">
      <image src="/static/images/abort1.svg" />
      停止回答
    </view>

    <view
      class="content"
      :class="{ 'keyboard-show': isInputting, 'full-screen': isFullScreen }"
    >
      <view class="actions" v-if="!currentAction">
        <view
          class="action"
          @click="exitArtificial"
          v-if="chatMode === 'artificial'"
        >
          <image src="/static/images/turn-off.svg" />
          结束人工
        </view>

        <template v-if="chatMode === 'assistant'">
          <view
            class="action"
            @click="currentAction = action"
            v-for="action in actions"
            :key="action.action"
          >
            <image :src="action.icon" />
            {{ action.title }}
          </view>
          <view class="action" @click="goLessons">
            <image src="/static/images/lessons.svg" />
            法律专题
          </view>
        </template>
      </view>

      <view class="in-action" v-if="currentAction">
        <view class="action-title-bar">
          <view class="action-title">
            <image :src="currentAction.icon" />
            {{ currentAction.title }}
          </view>
          <view class="close-btn" @click="currentAction = null">
            <image src="/static/images/close.svg" />
          </view>
        </view>
        <view class="action-content">
          <template v-if="currentAction.action === 'compliance'">
            <attachmentsUploader
              ref="attachmentsUploaderRef"
              v-model:attachments="attachments"
            />
          </template>
        </view>
      </view>

      <view class="input-box">
        <view
          class="call"
          @click="call"
          v-show="
            !currentAction && chatMode === 'assistant' && inputMode === 'text'
          "
        >
          <image src="/static/images/call.png" />
        </view>
        <view class="input-area">
          <inputComp
            :inputMode="inputMode"
            :currentAction="currentAction"
            :attachments="attachments"
            :disabled="disabled"
            :isGenerating="isGenerating"
            :keyboardHeight="keyboardHeight"
            @confirm="confirm"
            @toggleFullScreen="toggleFullScreen"
            @inputFocus="onInputFocus"
            @inputBlur="onInputBlur"
            @toggleInputMode="onToggleInputMode"
            @uploadAttachments="uploadAttachments"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.bottom-panel {
  width: 100%;
  position: fixed;
  z-index: 9;
  bottom: v-bind(floatBottom);
  left: 0;
  background: rgba(248, 250, 254, 0.8);
  box-shadow: 0rpx 32rpx 48rpx 0rpx rgba(160, 163, 189, 0.16);
  border-radius: 64rpx 64rpx 0rpx 0rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.abort {
  width: 208rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 108rpx 108rpx 108rpx 108rpx;
  border: 2rpx solid rgba(252, 252, 252, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #466fff;
  padding: 0 24rpx;
  box-sizing: border-box;
  position: absolute;
  top: -146rpx;
  left: 50%;
  transform: translateX(-50%);

  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
}

.content {
  padding: 24rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
  display: flex;
  flex-direction: column;
  gap: 24rpx;

  &.keyboard-show {
    padding-bottom: 24rpx;
  }

  &.full-screen {
    padding: 0;
  }

  .actions {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .action {
      width: 208rpx;
      height: 80rpx;
      line-height: 80rpx;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 255, 255, 0.7) 100%
      );
      box-shadow: 0rpx 32rpx 48rpx 0rpx rgba(160, 163, 189, 0.16);
      border-radius: 108rpx 108rpx 108rpx 108rpx;
      border: 2rpx solid rgba(252, 252, 252, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28rpx;
      font-weight: 500;
      color: #253a57;
      gap: 6rpx;

      image {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  .in-action {
    .action-title-bar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 24rpx;
      padding: 0 24rpx;

      .action-title {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 6rpx;
        font-size: 28rpx;

        image {
          width: 32rpx;
          height: 32rpx;
        }
      }

      .close-btn {
        flex-shrink: 0;
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        image {
          width: 48rpx;
          height: 48rpx;
        }
      }
    }
  }

  .input-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40rpx;

    .call {
      flex-shrink: 0;
      width: 128rpx;
      height: 128rpx;
      box-shadow: 0rpx 32rpx 48rpx 0rpx rgba(160, 163, 189, 0.16);
      border-radius: 64rpx 64rpx 64rpx 64rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .input-area {
      flex: 1;
    }
  }
}
</style>
