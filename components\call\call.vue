<script setup>
const props = defineProps({
  visible: {
    type: <PERSON><PERSON>an,
    default: false,
  },
  callStatus: {
    // 正在听 listen、正在想 think、正在说 speak、暂停 pause
    type: String,
    default: "",
  },
})

const emit = defineEmits(["pause", "resume", "abort", "exit"])

const pause = () => {
  emit("pause")
}
const resume = () => {
  emit("resume")
}
const abort = () => {
  emit("abort")
}
const exit = () => {
  emit("exit")
}

const togglePause = () => {
  if (props.callStatus === "pause") {
    resume()
  } else {
    pause()
  }
}
</script>

<template>
  <view class="call" v-if="visible">
    <view class="response">
      <view class="avatar" :class="{ paused: callStatus === 'pause' }">
        <image src="/static/images/logo.png" />
      </view>

      <view class="status-effect">
        <image
          style="width: 170rpx; height: 166rpx"
          v-if="callStatus === 'think'"
          src="/static/images/generating.gif"
        />
        <view class="speaking" v-else-if="callStatus === 'speak'">
          <wave2 :barsCount="3" active color="#466FFF" />
        </view>
      </view>
    </view>

    <view style="height: 140rpx">
      <view class="audio-status" v-if="callStatus === 'listen'">
        <wave2 :barsCount="4" active color="#ffffff" />
        <text class="text" style="color: #ffffff">正在听</text>
      </view>
      <view
        class="audio-status"
        v-else-if="['think', 'speak'].includes(callStatus)"
        @click="abort"
      >
        <image class="icon-abort" src="/static/images/abort-call.svg" />
        <text class="text">点击打断</text>
      </view>
      <view
        class="audio-status"
        v-else-if="callStatus === 'pause'"
        @click="resume"
      >
        <image class="icon-continue" src="/static/images/continue.svg" />
        <text class="text">点击继续</text>
      </view>
    </view>

    <view class="actions">
      <view
        class="action"
        :class="{ play: callStatus === 'pause' }"
        @click="togglePause"
      >
        <image
          v-if="callStatus === 'pause'"
          src="/static/images/action-play.svg"
        />
        <image v-else src="/static/images/action-pause.svg" />
      </view>
      <view class="action main" @click="exit">
        <image src="/static/images/action-close.svg" />
      </view>
      <view style="width: 120rpx; height: 120rpx"> </view>
    </view>
    <copyRight />
  </view>
</template>

<style lang="scss" scoped>
.call {
  width: 100vw;
  height: 100vh;
  background: #000000;
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 280rpx 96rpx 0;
}

.response {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  .avatar {
    width: 400rpx;
    height: 400rpx;
    border-radius: 50%;
    overflow: hidden;
    background-color: #f5f5f5;
    border: 12rpx solid #191919;

    &.paused {
      opacity: 0.75;
    }

    image {
      width: 100%;
      height: 100%;
    }
  }

  .status-effect {
    position: absolute;
    top: -54rpx;
    right: -48rpx;

    .speaking {
      width: 218rpx;
      height: 128rpx;
      background: #ffffff;
      box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
      border-radius: 48rpx 48rpx 48rpx 12rpx;

      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.audio-status {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .icon-abort {
    width: 50rpx;
    height: 50rpx;
  }
  .icon-continue {
    width: 60rpx;
    height: 60rpx;
  }

  .text {
    font-size: 32rpx;
    color: rgba(255, 255, 255, 0.3);
    font-weight: bold;
    margin-top: 30rpx;
  }
}

.actions {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 146rpx;
  gap: 86rpx;

  .action {
    width: 120rpx;
    height: 120rpx;
    background: rgba(120, 120, 128, 0.12);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;

    image {
      width: 48rpx;
      height: 48rpx;
    }

    &.main {
      width: 146rpx;
      height: 146rpx;
      background: #eb5446;
    }
  }

  .play {
    background: #ffffff;
  }
}
</style>
