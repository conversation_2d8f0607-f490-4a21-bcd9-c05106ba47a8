<template>
  <view class="dialog">
    <uni-popup ref="popupRef" type="center" @maskClick="cancel">
      <view class="dialog-content">
        <image
          class="dialog-close"
          src="/static/images/close.svg"
          @click="cancel"
        />
        <view class="dialog-body">
          <image src="/static/images/default-avatar.png" class="service-icon" />
          <view class="service-title">完善资料后使用完整功能~</view>
        </view>
        <view class="dialog-footer">
          <button class="dialog-footer-btn primary" @click="confirm">
            完善个人资料
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, toRef, watch, onMounted } from "vue"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(["cancel", "confirm", "update:visible"])

const visible = toRef(props, "visible")

const popupRef = ref(null)

watch(
  visible,
  (val) => {
    if (popupRef.value) {
      console.log(popupRef.value, val)
      if (val) {
        popupRef.value.open()
      } else {
        popupRef.value.close()
      }
    }
  },
  {
    immediate: true,
  }
)

const cancel = () => {
  emit("update:visible", false)
  emit("cancel")
}
const confirm = () => {
  emit("confirm")
}

onMounted(() => {
  if (visible.value) {
    popupRef.value.open()
  }
})
</script>

<style lang="scss" scoped>
.dialog-content {
  width: 668rpx;
  background: #ffffff;
  border-radius: 56rpx;
  position: relative;
}

.dialog-close {
  width: 48rpx;
  height: 48rpx;
  position: absolute;
  top: 44rpx;
  right: 44rpx;
}

.dialog-body {
  padding: 76rpx 88rpx 78rpx;

  display: flex;
  flex-direction: column;
  align-items: center;

  .service-icon {
    width: 158rpx;
    height: 158rpx;
    margin: 0 auto;
  }

  .service-title {
    font-weight: bold;
    font-size: 34rpx;
    color: #14142b;
    text-align: center;
    margin-top: 60rpx;
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  padding: 0 88rpx 98rpx;

  &-btn {
    width: 100%;
  }
}
</style>
