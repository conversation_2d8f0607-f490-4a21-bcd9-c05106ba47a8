<script setup>
import { ref, watch, computed, onMounted } from "vue"
import { useStore } from "/store"

const store = useStore()

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  defaultData: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(["update:visible", "confirm"])

const popup = ref(null)

const closePopup = () => {
  popup.value.close()
  emit("update:visible", false)
}

watch(
  () => props.visible,
  (value) => {
    if (value) {
      popup.value.open()
    } else {
      popup.value.close()
      reset()
    }
  }
)

watch(
  () => props.defaultData,
  (value) => {
    if (value) {
      score.value = value.score || 0
      selections.value = value.selections || []
      comment.value = value.comment || ""
    }
  }
)

const optionsMap = computed(() => {
  return {
    1: store.config.one_star_comment_options || [],
    2: store.config.two_star_comment_options || [],
    3: store.config.three_star_comment_options || [],
    4: store.config.four_star_comment_options || [],
    5: store.config.five_star_comment_options || [],
  }
})
const options = computed(() => optionsMap.value[score.value] || [])
const score = ref(0)
const selections = ref([])
const comment = ref("")
const scoreMap = {
  1: "非常不满意",
  2: "不满意",
  3: "一般",
  4: "满意",
  5: "非常满意",
}
const scoreText = computed(() => scoreMap[score.value])
const onRateChange = (value) => {
  if (props.defaultData) return
  let score = value
  selections.value = []
}
const onSelect = (option) => {
  if (props.defaultData) return
  if (selections.value.includes(option)) {
    selections.value = selections.value.filter((item) => item !== option)
  } else {
    selections.value.push(option)
  }
}

const reset = () => {
  score.value = 0
  selections.value = []
  comment.value = ""
}

const confirm = () => {
  emit("confirm", {
    score: score.value,
    selections: selections.value,
    comment: comment.value,
  })
}

onMounted(() => {
  console.log("onMounted", optionsMap.value)
})
</script>

<template>
  <uni-popup
    ref="popup"
    type="bottom"
    :safe-area="false"
    @maskClick="closePopup"
  >
    <view class="popup">
      <view class="popup-close" @click="closePopup">
        <image src="/static/images/close.svg" />
      </view>
      <view class="popup-title"> 反馈 </view>
      <view class="popup-content">
        <view class="rate-wrapper">
          <rate
            v-model="score"
            :size="94"
            :disabled="defaultData"
            @change="onRateChange"
          />
          <view class="rate-text">{{ scoreText }}</view>
        </view>
        <view class="checkbtn-wrapper">
          <view
            class="checkbtn"
            :class="{ active: selections.includes(option) }"
            v-for="option in options"
            :key="option"
            @click="onSelect(option)"
          >
            {{ option }}
          </view>
        </view>
        <view class="comment-wrapper">
          <textarea
            v-model="comment"
            :disabled="defaultData"
            placeholder="您的反馈将帮助我们进步~"
            class="comment"
            placeholder-style="color: #A0A3BD;"
          />
        </view>
        <view class="btn-wrapper">
          <button class="btn primary" :disabled="defaultData" @click="confirm">
            {{ defaultData ? "已反馈" : "提交" }}
          </button>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<style lang="scss" scoped>
.popup {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 64rpx 64rpx 0rpx 0rpx;
  box-shadow: 0rpx 32rpx 48rpx 0rpx rgba(160, 163, 189, 0.16);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  padding: 44rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  backdrop-filter: blur(16rpx);

  .popup-close {
    position: absolute;
    top: 40rpx;
    right: 40rpx;
    width: 40rpx;
    height: 40rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .popup-title {
    font-weight: bold;
    text-align: center;
    font-size: 36rpx;
    color: #14142b;
  }
  .popup-content {
    margin-top: 40rpx;
  }
}

.rate-wrapper {
  padding: 0 16rpx;

  .rate-text {
    font-weight: 500;
    font-size: 32rpx;
    color: #6e7191;
    text-align: center;
    margin-top: 42rpx;
  }
}

.checkbtn-wrapper {
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
  margin-top: 40rpx;
  gap: 18rpx 20rpx;

  .checkbtn {
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 34rpx;
    background: #f5f5f5;
    border-radius: 108rpx;
    border: 2rpx solid #d9d9d9;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 500;
    font-size: 32rpx;
    color: #253a57;

    &.active {
      background: #466fff;
      color: #ffffff;
      border-color: #466fff;
    }
  }
}

.comment-wrapper {
  margin-top: 40rpx;

  .comment {
    width: 100%;
    height: 218rpx;
    padding: 44rpx;
    box-sizing: border-box;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
    border-radius: 48rpx 48rpx 48rpx 48rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.5);
  }
}

.btn-wrapper {
  margin-top: 56rpx;

  .btn {
    width: 100%;
  }
}
</style>
