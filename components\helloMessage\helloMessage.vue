<script setup></script>

<template>
  <view class="hello-message" id="hello-message">
    <view class="avatar">
      <image src="/static/images/logo.png" mode="aspectFill" />
    </view>
    <view class="content">
      <view class="title">
        <view>你好！我是律小云</view>
        <view>劳动法问题终结者</view>
      </view>
      <view class="desc"> 你可以问我任何劳动法问题，如对我的回答不满意，我们的专业律师会帮你兜底解决吆 </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.hello-message {
  position: relative;
  padding: 54rpx 0;

  .avatar {
    width: 196rpx;
    height: 196rpx;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 36rpx;
    overflow: hidden;
    border-radius: 50%;
    background-color: #f5f5f5;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .content {
    padding: 40rpx;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
    border-radius: 12rpx 48rpx 48rpx 48rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.5);

    .title {
      font-size: 44rpx;
      font-weight: bold;
      color: #253a57;
      margin-bottom: 20rpx;
    }

    .desc {
      font-size: 30rpx;
      color: #6e7191;
    }
  }
}
</style>
