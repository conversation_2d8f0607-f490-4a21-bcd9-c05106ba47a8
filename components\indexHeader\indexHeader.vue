<script setup>
import { rpx2px } from "/utils"

const props = defineProps({
  leftTimes: {
    type: Number,
    default: 0,
  },
  chatMode: {
    type: String,
    default: "assistant",
  },
  isMute: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(["toggleMute"])

const { top, height } = uni.getMenuButtonBoundingClientRect()
const headerTop = top - (rpx2px(80) - height) / 2

const toHome = () => {
  uni.navigateTo({
    url: "/pages/mine/mine",
  })
}

const toRecharge = () => {
  uni.navigateTo({
    url: "/pages/recharge/recharge",
  })
}
</script>

<template>
  <view class="index-header" :style="{ paddingTop: `${headerTop}px` }">
    <view class="content">
      <view class="left">
        <!-- <image src="/static/images/logo.png" class="logo" />
        <view class="title">
          <view class="main-title">律小云</view>
          <view class="sub-title">AI法律助手</view>
        </view> -->
        <view class="action" @click="toHome">
          <image src="/static/images/menu.svg" class="menu" />
        </view>

        <view
          class="action"
          v-if="chatMode === 'assistant'"
          @click="emit('toggleMute')"
        >
          <image src="/static/images/mute.svg" v-if="isMute" />
          <image src="/static/images/unmute.svg" v-else />
        </view>
      </view>
      <view class="center" v-if="leftTimes <= 10" @click="toRecharge">
        <text class="label">剩余次数:</text>
        <text class="value">{{ leftTimes }}</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.index-header {
  width: 100%;
  position: fixed;
  top: 0;

  .content {
    height: 80rpx;
    padding: 0 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    .left {
      position: absolute;
      left: 24rpx;
      display: flex;
      align-items: center;
      gap: 48rpx;

      .action {
        width: 48rpx;
        height: 48rpx;

        image {
          width: 100%;
          height: 100%;
        }
      }

      .logo {
        width: 80rpx;
        height: 80rpx;
        background-color: #f5f5f5;
        border-radius: 50%;
      }
      .title {
        margin-left: 12rpx;
        .main-title {
          font-size: 28rpx;
          color: #253a57;
        }
        .sub-title {
          font-size: 24rpx;
          color: rgba(0, 0, 0, 0.5);
        }
      }
    }

    .center {
      margin: 8rpx auto;
      width: 202rpx;
      height: 64rpx;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 255, 255, 0.7) 100%
      );
      box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
      border-radius: 108rpx 108rpx 108rpx 108rpx;
      border: 2rpx solid rgba(255, 255, 255, 0.5);
      font-size: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .label {
        color: #6e7191;
        margin-right: 12rpx;
      }
      .value {
        color: #253a57;
        font-weight: bold;
      }
    }
  }
}
</style>
