<script setup>
import { ref, computed, onMounted, toRef, nextTick } from "vue"
import { useStore } from "/store.js"

const store = useStore()

const props = defineProps({
  keyboardHeight: {
    type: String,
    default: "0px",
  },
  isGenerating: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  inputMode: {
    type: String,
    default: "text",
  },
  currentAction: {
    type: Object,
    default: null,
  },
  attachments: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits([
  "confirm",
  "toggleFullScreen",
  "inputFocus",
  "inputBlur",
  "toggleInputMode",
  "uploadAttachments",
])

const keyboardHeight = toRef(props, "keyboardHeight")
const headerHeight = computed(() => store.headerHeight)

const inputMode = toRef(props, "inputMode")
const toggleInputMode = () => {
  emit("toggleInputMode")
}

const text = ref("")

const isFullScreen = ref(false)
const toggleFullScreen = () => {
  isFullScreen.value = !isFullScreen.value
  emit("toggleFullScreen", isFullScreen.value)

  isFocus.value = false
  nextTick(() => {
    setTimeout(() => {
      isFocus.value = true
    }, 100)
  })
}
const fullScreenBtnVisible = computed(() => text.value.length > 30)

const isFocus = ref(false)
const onFocus = () => {
  isFocus.value = true
  emit("inputFocus")
}
const onBlur = () => {
  isFocus.value = false
  emit("inputBlur")
}
const onRootClick = () => {
  if (inputMode === "text") {
    isFocus.value = true
  }
}

const requestPermission = () => {
  uni.getSetting({
    success(res) {
      if (!res.authSetting["scope.record"]) {
        uni.authorize({
          scope: "scope.record",
          success() {
            hasRecordPermission.value = true
          },
          fail() {
            uni.showModal({
              title: "提示",
              content: "请授权录音权限",
              success(res) {
                if (res.confirm) {
                  uni.openSetting()
                }
              },
            })
          },
        })
      } else {
        hasRecordPermission.value = true
      }
    },
  })
}

const hasRecordPermission = ref(false)
const isRecording = ref(false)
const cancelRecording = () => {
  isRecording.value = false
}
const voiceInputRef = ref(null)
const onInputBtnTouchStart = (e) => {
  console.log("onInputBtnTouchStart", e)

  if (!hasRecordPermission.value) {
    requestPermission()
    return
  }

  if (props.isGenerating) return
  isRecording.value = true
  if (inputMode.value === "voice") {
    voiceInputRef.value.onTouchStart(e)
  }
}
const onInputBtnTouchMove = (e) => {
  console.log("onInputBtnTouchMove", e)
  if (!hasRecordPermission.value) {
    requestPermission()
    return
  }
  if (props.isGenerating) return
  if (inputMode.value === "voice") {
    voiceInputRef.value.onTouchMove(e)
  }
}
const onInputBtnTouchEnd = (e) => {
  console.log("onInputBtnTouchEnd", e)
  if (!hasRecordPermission.value) {
    requestPermission()
    return
  }
  if (props.isGenerating) return
  if (inputMode.value === "voice") {
    voiceInputRef.value.onTouchEnd(e)
  }
}

const placeholder = computed(() => {
  if (props.currentAction) {
    return props.currentAction.placeholder
  }
  return "发消息..."
})

const uploadBtnVisible = computed(() => {
  if (props.currentAction) {
    return props.currentAction.action === "compliance"
  }
  return false
})

const confirmBtnVisible = computed(() => {
  if (props.currentAction) {
    if (props.currentAction.action === "compliance") {
      return props.attachments.length > 0
    }
  }
  return text.value.length > 0
})

const confirm = (val) => {
  emit("confirm", val)
  text.value = ""
  isFullScreen.value = false
}

onMounted(() => {
  uni.getSetting({
    success(res) {
      if (!res.authSetting["scope.record"]) {
        hasRecordPermission.value = false
        console.log("没有录音权限")
      } else {
        hasRecordPermission.value = true
        console.log("有录音权限")
      }
    },
  })
})
</script>

<template>
  <view class="input-comp">
    <view
      class="text-input"
      :class="{ full: isFullScreen }"
      v-show="!isRecording"
      @click="onRootClick"
    >
      <view class="input-content">
        <view class="input">
          <textarea
            v-show="inputMode === 'text'"
            :focus="isFocus"
            @focus="onFocus"
            @blur="onBlur"
            :placeholder="placeholder"
            placeholder-style="color: #A0A3BD; font-size: 32rpx; font-weight: 500"
            auto-height
            fixed
            :maxlength="-1"
            :adjust-position="false"
            cursor-color="#466FFF"
            confirm-type="send"
            :show-confirm-bar="false"
            disable-default-padding
            :disabled="disabled"
            v-model="text"
          />
          <view
            v-show="inputMode === 'voice' && !isRecording"
            class="voice-placeholder"
            @touchstart="onInputBtnTouchStart"
            @touchmove="onInputBtnTouchMove"
            @touchend="onInputBtnTouchEnd"
          >
            按住说话
          </view>
        </view>
        <view class="actions">
          <view>
            <button
              @click.stop="toggleFullScreen"
              class="full-screen"
              v-if="fullScreenBtnVisible"
            >
              <image v-if="isFullScreen" src="/static/images/collapse.svg" />
              <image v-else src="/static/images/expand.svg" />
            </button>
          </view>

          <button
            class="send"
            @click.stop="confirm(text)"
            v-if="confirmBtnVisible"
          >
            <image src="/static/images/send.svg" />
          </button>
          <view class="mode-container" v-else>
            <button
              class="upload"
              @click="emit('uploadAttachments')"
              v-if="uploadBtnVisible"
            >
              <image
                class="mode-icon"
                src="/static/images/add-attachment.svg"
              />
            </button>
            <button
              class="mode"
              @click="toggleInputMode"
              v-if="currentAction === null"
            >
              <image
                src="/static/images/voice.svg"
                class="mode-icon"
                v-if="inputMode === 'text'"
              />
              <image
                src="/static/images/keyboard.svg"
                class="mode-icon"
                v-else
              />
            </button>
          </view>
        </view>
      </view>
    </view>
    <voiceInput
      v-show="inputMode === 'voice' && isRecording"
      ref="voiceInputRef"
      :disabled="disabled"
      @confirm="confirm"
      @cancel="cancelRecording"
    />
  </view>
</template>

<style lang="scss" scoped>
.input-comp {
  width: 100%;
}

.text-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  min-height: 128rpx;

  .input-content {
    flex: 1;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
    border-radius: 48rpx 48rpx 48rpx 48rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.5);
    box-sizing: border-box !important;

    display: flex;
    gap: 44rpx;
    padding: 0 22rpx;
    align-items: flex-start;

    .input {
      flex: 1;
      box-sizing: border-box;
      margin: 44rpx 0;
      // padding-left: 22rpx;
      margin-left: 22rpx;

      textarea {
        width: 100%;
        border: none;
        background: none;
        font-size: 32rpx;
        font-weight: 500;
        resize: none;

        // 隐藏滚动条
        &::-webkit-scrollbar {
          display: none;
        }
      }

      .voice-placeholder {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        font-size: 32rpx;
        font-weight: bold;
        color: #000000;
        line-height: 128rpx;
        text-align: center;
      }
    }

    .actions {
      display: flex;
      flex-shrink: 0;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      align-self: stretch;
      padding: 24rpx 0;
      position: relative;
      z-index: 1;

      button {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .full-screen {
        image {
          margin: 18rpx 0;
          width: 40rpx;
          height: 40rpx;
        }
      }

      .send {
        border-radius: 50%;
        overflow: hidden;

        image {
          width: 100%;
          height: 100%;
        }
      }

      .mode-icon {
        width: 56rpx;
        height: 56rpx;
      }

      .mode-container {
        display: flex;
        align-items: center;
      }
    }
  }

  &.full {
    background-color: #fff;
    padding: 26rpx;
    border-radius: 64rpx 64rpx 0rpx 0rpx;

    .input-content {
      background: unset;
      box-shadow: unset;
      border: unset;
      height: calc(
        100vh - v-bind(headerHeight) - v-bind(keyboardHeight) - 88rpx
      );
    }
  }
}
</style>
