<script setup>
import { computed } from "vue"

const props = defineProps({
  status: {
    type: String,
    default: "loadMore",
  },
})

const statusMap = {
  loadMore: "加载更多",
  loading: "加载中...",
  noMore: "没有更多了~",
}

const statusText = computed(() => statusMap[props.status])
</script>

<template>
  <view class="load-more-text">
    {{ statusText }}
  </view>
</template>

<style lang="scss" scoped>
.load-more-text {
  text-align: center;
  padding: 40rpx 0;
  color: rgba(110, 113, 145, 0.5);
  font-size: 24rpx;
}
</style>
