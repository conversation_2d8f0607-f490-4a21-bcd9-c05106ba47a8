<script setup>
import markdownit from "markdown-it"

import iconDocx from "@/static/images/file-icon-docx.svg"
import iconPdf from "@/static/images/file-icon-pdf.svg"

const md = markdownit()

const props = defineProps({
  message: {
    type: Object,
    required: true,
  },
  mode: {
    type: String,
    required: true,
  },
  isLast: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(["copy", "speak", "rate", "manual", "refresh"])

const onCopy = () => {
  emit("copy")
}

const onSpeak = () => {
  emit("speak")
}

const onRate = () => {
  emit("rate")
}

const onManual = () => {
  emit("manual")
}

const onRefresh = () => {
  emit("refresh")
}

const onAttachment = (res_attachment) => {
  uni.downloadFile({
    url: res_attachment.full_path,
    success: (res) => {
      console.log("res", res)
      uni.openDocument({
        filePath: res.tempFilePath,
        fileType: res_attachment.ext,
      })
    },
    fail: (err) => {
      console.log("err", err)
    },
    complete: () => {
      console.log("complete")
    },
  })
}
</script>
<template>
  <view class="message-content" :class="message.role">
    <template v-if="message.status === 'done' || mode === 'history'">
      <mp-html
        :tag-style="{
          p: 'margin-bottom: 40rpx;',
          li: 'margin-bottom: 40rpx;',
        }"
        :content="md.render(message.content)"
      ></mp-html>
    </template>
    <jp-typewriter
      v-else-if="message.status === 'output'"
      :text="message.content"
      :speed="100"
      :textStyle="{
        color: '#253A57',
        fontWeight: 'normal',
        fontSize: '34rpx',
      }"
    >
    </jp-typewriter>

    <view class="attachment" v-if="message.res_attachment" @click="onAttachment(message.res_attachment)">
      <image
        :src="iconDocx"
        v-if="message.res_attachment.ext === 'docx'"
        class="attachments-uploader-item-icon"
      />
      <image
        :src="iconPdf"
        v-else-if="message.res_attachment.ext === 'pdf'"
        class="attachments-uploader-item-icon"
      />

      <view class="attachments-uploader-item-name">
        {{ message.res_attachment.filename }}
      </view>
    </view>

    <view
      v-if="message.status === 'done' || mode === 'history'"
      class="actions"
    >
      <view class="left">
        <view class="action copy" @click="onCopy">
          <image src="/static/images/answer-action-copy.svg" />
        </view>
        <view class="action speak" @click="onSpeak">
          <image
            v-if="message.voiceStatus === 'loading'"
            src="/static/images/answer-action-speak-loading.gif"
          />
          <image
            v-else-if="message.voiceStatus === 'active'"
            src="/static/images/answer-action-speak-active.gif"
          />
          <image v-else src="/static/images/answer-action-speak.svg" />
        </view>
        <view class="action rate" v-if="message.record_id" @click="onRate">
          <image src="/static/images/answer-action-rate.png" />
        </view>
        <view
          class="action manual"
          v-if="message.role === 'assistant'"
          @click="onManual"
        >
          <image src="/static/images/answer-action-manual.svg" />
        </view>
      </view>
      <view
        class="right"
        v-if="mode === 'chat' && isLast && message.role === 'assistant'"
      >
        <view class="action refresh" @click="onRefresh">
          <image src="/static/images/answer-action-refresh.svg" />
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.message-content {
  font-size: 34rpx;
  line-height: 54rpx;
  padding: 30rpx;
  text-align: justify;

  &.user {
    background: #466fff;
    border-radius: 48rpx 48rpx 12rpx 48rpx;
    color: #ffffff;
    width: fit-content;
    max-width: 100%;
  }

  &.artificial,
  &.assistant {
    width: 100%;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
    padding: 30rpx;
    border-radius: 12rpx 48rpx 48rpx 48rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.5);
    color: #253a57;

    .actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-top: 40rpx;

      .left,
      .right {
        display: flex;
        align-items: center;
        gap: 24rpx;

        .action {
          width: 80rpx;
          height: 80rpx;
          border-radius: 24rpx 24rpx 24rpx 24rpx;
          background: rgba(70, 111, 255, 0.06);
          display: flex;
          justify-content: center;
          align-items: center;

          image {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }
    }
  }

  .attachment {
    width: 100%;
    height: 180rpx;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    border: 2rpx solid rgba(252, 252, 252, 0.5);
    padding: 30rpx;
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 30rpx;

    .attachments-uploader-item-icon {
      width: 104rpx;
      height: 104rpx;
      flex-shrink: 0;
    }

    .attachments-uploader-item-name {
      flex: 1;
      font-size: 34rpx;
      color: #253a57;
      line-clamp: 2;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
