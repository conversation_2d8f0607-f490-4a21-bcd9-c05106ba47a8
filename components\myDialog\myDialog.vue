<template>
  <view class="dialog">
    <uni-popup ref="popupRef" type="center" @maskClick="cancel">
      <view class="dialog-content">
        <image
          class="dialog-close"
          src="/static/images/close.svg"
          @click="cancel"
        />
        <view class="dialog-body">{{ title }}</view>
        <view class="dialog-footer">
          <button
            class="dialog-footer-btn confirm-btn"
            @click="confirm"
            :loading="confirmLoading"
          >
            {{ confirmText }}
          </button>
          <button class="dialog-footer-btn cancel-btn" @click="cancel" v-if="showCancel">
            {{ cancelText }}
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, toRef, watch } from "vue"

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  title: {
    // 提示标题
    type: String,
    default: "提示",
  },
  showCancel: {
    // 是否显示取消按钮
    type: Boolean,
    default: true,
  },
  cancelText: {
    // 取消按钮文字
    type: String,
    default: "取消",
  },
  cancelTextColor: {
    // 取消按钮颜色
    type: String,
    default: "#253A57",
  },
  cancelColor: {
    // 取消按钮颜色
    type: String,
    default: "#F1F1F1",
  },
  confirmText: {
    // 确认按钮文字
    type: String,
    default: "确定",
  },
  confirmTextColor: {
    // 确认按钮文字颜色
    type: String,
    default: "#fff",
  },
  confirmColor: {
    // 确认按钮颜色
    type: String,
    default: "#EB5446",
  },
  confirmLoading: {
    // 确认按钮loading
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(["cancel", "confirm", "update:show"])

const show = toRef(props, "show")

const popupRef = ref(null)

watch(
  show,
  (val) => {
    if (popupRef.value) {
      console.log(popupRef.value, val)
      if (val) {
        popupRef.value.open()
      } else {
        popupRef.value.close()
      }
    }
  },
  {
    immediate: true,
  }
)

const cancel = () => {
  emit("update:show", false)
  emit("cancel")
}
const confirm = () => {
  emit("confirm")
}
</script>

<style lang="scss" scoped>
.dialog-content {
  width: 668rpx;
  height: 464rpx;
  background: #ffffff;
  border-radius: 56rpx;
  position: relative;
}

.dialog-close {
  width: 48rpx;
  height: 48rpx;
  position: absolute;
  top: 44rpx;
  right: 44rpx;
}

.dialog-body {
  padding: 122rpx 38rpx 78rpx;
  font-size: 40rpx;
  line-height: 64rpx;
  text-align: center;
  color: #14142b;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 24rpx;
  padding: 0 38rpx 38rpx 38rpx;

  &-btn {
    flex: 1;
    height: 128rpx;
    line-height: 128rpx;
    font-size: 36rpx;
    border-radius: 48rpx;

    &.cancel-btn {
      color: v-bind(cancelTextColor);
      background-color: v-bind(cancelColor);
    }

    &.confirm-btn {
      color: v-bind(confirmTextColor);
      /* v-bind: 绑定setup中的属性 */
      background-color: v-bind(confirmColor);
    }
  }
}
</style>
