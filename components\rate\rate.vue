<script setup>
import activeIcon from "/static/images/star-active.svg"
import inactiveIcon from "/static/images/star-inactive.svg"

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
  size: {
    type: Number,
    default: 40,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(["update:modelValue", "change"])

const handleClick = (index) => {
  if (props.disabled) return
  emit("update:modelValue", index)
  emit("change", index)
}
</script>

<template>
  <view class="rate">
    <image
      v-for="index in 5"
      :key="index"
      :src="index <= modelValue ? activeIcon : inactiveIcon"
      :style="{ width: `${size}rpx`, height: `${size}rpx` }"
      @click="handleClick(index)"
    />
  </view>
</template>

<style lang="scss" scoped>
.rate {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
</style>
