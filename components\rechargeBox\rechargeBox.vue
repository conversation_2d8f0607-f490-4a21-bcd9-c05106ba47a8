<script setup>
import { ref, computed } from "vue"
import { useStore } from "/store"
import request from "/request"

const store = useStore()

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
  packageList: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(["update:modelValue", "success"])

const activePackage = computed(
  () => props.packageList[props.modelValue] || null
)
const selectPackage = (index) => {
  emit("update:modelValue", index)
}

const isAgree = ref(false)
const toggleAgree = () => {
  isAgree.value = !isAgree.value
}
const goAgreement = () => {
  console.log("goAgreement", store.serviceArticleId)
  uni.navigateTo({
    url: `/pages/article/article?id=${store.serviceArticleId}`,
  })
}

const recharge = async () => {
  if (!store.userInfo.phone) {
    uni.showModal({
      title: "请先完善个人信息",
      icon: "none",
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: "/pages/profile/profile",
          })
        }
      },
    })
    return
  }
  if (!isAgree.value) {
    uni.showToast({
      title: "请先同意用户协议",
      icon: "none",
    })
    return
  }

  const res = await request({
    url: "/mini/order_create",
    method: "post",
    data: {
      package_id: props.packageList[props.modelValue].id,
    },
  })

  console.log(res)

  if (res.code === 200) {
    uni.requestPayment({
      provider: "wxpay",
      timeStamp: res.result.timeStamp,
      nonceStr: res.result.nonceStr,
      package: res.result.package,
      signType: res.result.signType,
      paySign: res.result.paySign,
      success: (res) => {
        emit("success")
        uni.showToast({
          title: "充值成功",
          icon: "success",
        })
        store.getUserInfo()
      },
      fail: (err) => {
        uni.showToast({
          title: "充值失败",
          icon: "none",
        })
      },
    })
  }
}
</script>

<template>
  <view class="recharge">
    <view class="package-list">
      <view
        class="package-item"
        :class="{ active: index === modelValue }"
        v-for="(item, index) in packageList"
        :key="index"
        @click="selectPackage(index)"
      >
        <view class="times"> {{ item.times }}次</view>
        <view class="price">
          <text class="currency">￥</text>
          {{ parseFloat(item.price) }}
        </view>
        <view class="validity">{{ item.description }}</view>
      </view>
    </view>
    <view class="agree" :class="{ active: isAgree }">
      <checkbox-group @click="toggleAgree">
        <image src="/static/images/check-icon.svg" class="check-icon" />
        <image src="/static/images/uncheck-icon.svg" class="uncheck-icon" />

        <text>购买即表示您已同意</text>
        <text class="link" @click.stop="goAgreement">服务协议</text>
      </checkbox-group>
    </view>
    <view class="recharge-box">
      <view class="recharge-box-title">
        <text>合计：</text>
        <view class="price">
          <text class="currency"> ￥ </text>
          {{ activePackage?.price || 0 }}</view
        >
      </view>
      <button class="btn" @click="recharge">立即充值</button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.package-list {
  display: flex;
  flex-wrap: wrap;
  gap: 38rpx;

  .package-item {
    width: 196rpx;
    height: 228rpx;
    background: #ffffff;
    border-radius: 48rpx;
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    .times {
      font-size: 28rpx;
      font-weight: 500;
      color: #6e7191;
    }
    .price {
      font-size: 60rpx;
      font-weight: bold;
      color: #253a57;
      display: flex;
      align-items: baseline;
      gap: -4rpx;

      .currency {
        font-size: 500;
        font-size: 32rpx;
      }
    }
    .validity {
      font-size: 22rpx;
      color: #d0d3e5;
    }

    &.active {
      background: #fffbf5;
      box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(221, 189, 144, 0.2);
      border-radius: 48rpx 48rpx 48rpx 48rpx;
      border: 3rpx solid #ddbd90;

      .times {
        color: #83614d;
      }
      .price {
        color: #83614d;
      }
      .validity {
        color: #83614d;
      }
    }
  }
}

.agree {
  margin-top: 58rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 28rpx;

  checkbox-group {
    display: flex;
    align-items: center;
    gap: 8rpx;
    font-size: 28rpx;
    color: #6e7191;
  }

  text {
    font-size: 28rpx;
    color: #6e7191;
  }
  .link {
    text-decoration: underline;
  }

  .check-icon,
  .uncheck-icon {
    width: 48rpx;
    height: 48rpx;
  }

  .check-icon {
    display: none;
  }
  .uncheck-icon {
    display: block;
  }

  &.active {
    .check-icon {
      display: block;
    }
    .uncheck-icon {
      display: none;
    }
  }
}

.recharge-box {
  margin-top: 58rpx;
  height: 128rpx;
  background: #352b25;
  box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
  border-radius: 48rpx 48rpx 48rpx 48rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx;
  padding-left: 62rpx;

  .recharge-box-title {
    display: flex;
    align-items: baseline;
    font-size: 22rpx;
    font-weight: 500;
    color: #d0d3e5;
    text-align: center;
    .price {
      color: #e9c49c;
      font-weight: 500;
      display: flex;
      align-items: baseline;
      font-size: 44rpx;

      .currency {
        font-size: 22rpx;
      }
    }
  }
  .btn {
    width: 258rpx;
    height: 96rpx;
    line-height: 96rpx;
    background: linear-gradient(270deg, #ddbd90 0%, #e9c49c 100%);
    box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
    border-radius: 32rpx 32rpx 32rpx 32rpx;

    font-weight: 500;
    font-size: 36rpx;
    color: #362c26;
  }
}
</style>
