<script setup>
import { ref, watch, computed, onMounted } from "vue"
import request from "/request"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(["update:visible"])

const popup = ref(null)

const activePackageIndex = ref(0)

const packageList = ref([])
const loadPackageList = async () => {
  const res = await request({
    url: "/mini/package",
    method: "get",
    data: {
      size: 99,
    },
  })
  packageList.value = res.result?.data || []
}

const closePopup = () => {
  popup.value.close()
  emit("update:visible", false)
}

const goMore = () => {
  uni.navigateTo({
    url: "/pages/recharge/recharge",
  })
}

const onRechargeSuccess = () => {
  emit("update:visible", false)
}

watch(
  () => props.visible,
  (value) => {
    if (value) {
      if (!packageList.value.length) {
        loadPackageList()
      }
      popup.value.open()
    }
  }
)

onMounted(() => {
  loadPackageList()
  if (props.visible) {
    popup.value.open()
  }
})
</script>

<template>
  <uni-popup
    ref="popup"
    type="bottom"
    :safe-area="false"
    @maskClick="closePopup"
  >
    <view class="popup">
      <view class="popup-more" @click="goMore">
        <image src="/static/images/more.svg" />
      </view>
      <view class="popup-title">
        <view class="main-title">咨询次数已用完。</view>
        <view class="sub-title">充值后可继续雇佣律小云~</view>
      </view>
      <view class="popup-content">
        <rechargeBox
          :packageList="packageList"
          v-model="activePackageIndex"
          @success="onRechargeSuccess"
        />
      </view>
    </view>
  </uni-popup>
</template>

<style lang="scss" scoped>
.popup {
  background: rgba(255, 255, 255, 0.88);
  border-radius: 64rpx 64rpx 0rpx 0rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  padding: 40rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  backdrop-filter: blur(16rpx);

  .popup-more {
    width: 80rpx;
    height: 80rpx;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    box-shadow: 0rpx 32rpx 48rpx 0rpx rgba(160, 163, 189, 0.16);
    border-radius: 108rpx 108rpx 108rpx 108rpx;
    border: 2rpx solid rgba(252, 252, 252, 0.5);

    position: absolute;
    top: 24rpx;
    right: 24rpx;

    display: flex;
    justify-content: center;
    align-items: center;

    image {
      width: 48rpx;
      height: 48rpx;
    }
  }
  .popup-title {
    .main-title {
      font-size: 40rpx;
      color: #253a57;
      font-weight: bold;
    }

    .sub-title {
      font-size: 28rpx;
      color: #6e7191;
      margin-top: 12rpx;
    }
  }
  .popup-content {
    margin-top: 40rpx;
  }
}
</style>
