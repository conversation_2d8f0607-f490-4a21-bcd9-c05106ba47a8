<script setup>
import { ref, onMounted } from "vue"
import request from "/request"

const emit = defineEmits(["change"])

const regionList = ref([[], [], []])
const regionIndex = ref([0, 0, 0])

const loadData = (parent_id) => {
  return new Promise((resolve, reject) => {
    request({
      url: "/mini/region",
      method: "get",
      data: {
        parent_id,
      },
    }).then((res) => {
      resolve(res.result || [])
    })
  })
}

const loadInitRegionList = async () => {
  regionList.value[0] = await loadData(0)
  regionList.value[1] = await loadData(regionList.value[0][0].id)
  regionList.value[2] = await loadData(regionList.value[1][0].id)
}

const onColumnChange = async (e) => {
  const { column, value } = e.detail
  regionIndex.value[column] = value
  if (column === 0) {
    regionList.value[1] = []
    regionList.value[2] = []
    regionList.value[1] = await loadData(regionList.value[0][value].id)
    regionList.value[2] = await loadData(regionList.value[1][0].id)
    regionIndex.value[1] = 0
    regionIndex.value[2] = 0
  } else if (column === 1) {
    regionList.value[2] = []
    regionList.value[2] = await loadData(regionList.value[1][value].id)
    regionIndex.value[2] = 0
  }
}

const onChange = (e) => {
  const { value } = e.detail
  regionIndex.value = value
  const province = regionList.value[0][value[0]]
  const city = regionList.value[1][value[1]]
  const district = regionList.value[2][value[2]]
  emit("change", {
    province,
    city,
    district,
  })
}

onMounted(() => {
  loadInitRegionList()
})
</script>

<template>
  <picker
    mode="multiSelector"
    :value="regionIndex"
    :range="regionList"
    range-key="name"
    @columnchange="onColumnChange"
    @change="onChange"
  >
    <slot></slot>
  </picker>
</template>

<style lang="scss" scoped></style>
