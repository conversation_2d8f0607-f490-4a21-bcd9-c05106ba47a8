<script setup>
import { ref, computed, onMounted, inject, watch } from "vue"

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
})

const st = inject("st")

const emit = defineEmits(["confirm", "cancel"])

const viewState = ref("recording") // recording | cancelling

const viewStateTipsMap = {
  recording: "松手发送，上移取消",
  cancelling: "松手取消",
}

const viewStateTips = computed(() => viewStateTipsMap[viewState.value])

let oringinY = 0
let msgWillSend = false

const onTouchStart = (e) => {
  if (props.disabled) return
  st.value?.startST()
  oringinY = e.touches[0].clientY
  viewState.value = "recording"
}

const onTouchMove = (event) => {
  if (event.touches[0].clientY - oringinY < -50) {
    viewState.value = "cancelling"
  } else {
    viewState.value = "recording"
  }
}

const onTouchEnd = (e) => {
  emit("cancel")
  if (viewState.value === "cancelling") {
    msgWillSend = false
  } else {
    msgWillSend = true
  }
  st.value?.stopST()
  viewState.value = "recording"
}

let resStr = ""

const listenSt = () => {
  st.value?.onStChange((name, msg) => {
    // started changed completed begin end closed failed
    if (name === "started") {
      resStr = ""
    }
    if (name === "end") {
      const text = msg.payload?.result
      if (text) {
        resStr += text
      }
    }
    if (name === "completed") {
      if (msgWillSend) {
        if (resStr.trim()) {
          confirm(resStr)
        } else {
          uni.showToast({
            title: "没有听清楚你说的话",
            icon: "none",
          })
        }
        msgWillSend = false
      }
    }
    if (name === "failed") {
      if (msgWillSend) {
        uni.showToast({
          title: "没有听清楚你说的话",
          icon: "none",
        })
      }
      msgWillSend = false
    }
  })
}

const confirm = (text) => {
  console.log(text)
  emit("confirm", text)
}

watch(
  () => st.value,
  (newVal) => {
    if (newVal && newVal.onStChange) {
      listenSt()
    }
  },
  { immediate: true }
)

onMounted(() => {})

defineExpose({
  onTouchStart,
  onTouchMove,
  onTouchEnd,
})
</script>

<template>
  <view class="voice-input">
    <view class="voice-tips">{{ viewStateTips }}</view>
    <view
      class="voice-btn"
      @touchstart="onTouchStart"
      @touchmove="onTouchMove"
      @touchend="onTouchEnd"
      :class="`state_${viewState}`"
    >
      <wave
        :barsCount="30"
        :active="viewState === 'recording'"
        color="#ffffff"
      ></wave>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.voice-input {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.voice-tips {
  padding: 20rpx 0 44rpx;
  font-size: 24rpx;
  color: #6e7191;
  font-weight: 500;
}

.voice-btn {
  width: 100%;
  height: 128rpx;

  border-radius: 48rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
  display: flex;
  justify-content: center;
  align-items: center;

  &.state_recording {
    background: #466fff;
  }

  &.state_cancelling {
    background: #eb5446;
  }
}
</style>
