<template>
  <view class="spinner" :class="{ 'spinner--active': active }">
    <view
      v-for="n in barsCount"
      :key="n"
      :style="{
        '-webkit-animation-delay': getDelay(n) + 's',
        animationDelay: getDelay(n) + 's',
      }"
      class="bar"
    ></view>
  </view>
</template>

<script setup>
import { ref, computed } from "vue"
const props = defineProps({
  active: {
    type: Boolean,
    default: false,
  },
  color: {
    type: String,
    default: "#333",
  },
  barsCount: {
    type: Number,
    default: 5,
  },
})

const getDelay = (n) => {
  // 随机延迟 1s - 1s
  return Math.random()
}
</script>

<style lang="scss" scoped>
.spinner {
  height: 50rpx;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner > view {
  background-color: v-bind(color);
  width: 33rpx;
  height: 50rpx;
  display: inline-block;
  border-radius: 16rpx;
  margin: 0 2rpx;
}

.spinner--active > view {
  -webkit-animation: stretchdelay 1.2s infinite ease-in-out;
  animation: stretchdelay 1.2s infinite ease-in-out;
}

@-webkit-keyframes stretchdelay {
  0%,
  40%,
  100% {
    -webkit-transform: scaleY(0.7);
  }
  20% {
    -webkit-transform: scaleY(1);
  }
}

@keyframes stretchdelay {
  0%,
  40%,
  100% {
    transform: scaleY(0.7);
    -webkit-transform: scaleY(0.7);
  }
  20% {
    transform: scaleY(1);
    -webkit-transform: scaleY(1);
  }
}
</style>
