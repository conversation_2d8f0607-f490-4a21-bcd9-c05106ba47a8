<template>
  <view class="spinner" :class="{ 'spinner--active': active }">
    <view
      v-for="n in barsCount"
      :key="n"
      :style="{
        '-webkit-animation-delay': getDelay(n) + 's',
        animationDelay: getDelay(n) + 's',
        height: getBarHeight(n),
      }"
      class="bar"
    ></view>
  </view>
</template>

<script setup>
import { ref, computed } from "vue"
const props = defineProps({
  active: {
    type: Boolean,
    default: false,
  },
  size: {
    type: Number,
    default: 60,
  },
  color: {
    type: String,
    default: "#333",
  },
  barsCount: {
    type: Number,
    default: 5,
  },
})

const height = computed(() => {
  return props.size + "rpx"
})

const getDelay = (n) => {
  // 随机延迟 1s - 1s
  return Math.random()
}
const getBarHeight = (n) => {
  // 随机高度百分比 30% - 120%
  return Math.random() * 90 + 30 + "%"
}
</script>

<style lang="scss" scoped>
.spinner {
  height: v-bind(height);
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner > view {
  background-color: v-bind(color);
  width: 6rpx;
  display: inline-block;
  border-radius: 12rpx;
  margin: 0 4rpx;
}

.spinner--active > view {
  -webkit-animation: stretchdelay 1.2s infinite ease-in-out;
  animation: stretchdelay 1.2s infinite ease-in-out;
}

@-webkit-keyframes stretchdelay {
  0%,
  40%,
  100% {
    -webkit-transform: scaleY(0.6);
  }
  20% {
    -webkit-transform: scaleY(1);
  }
}

@keyframes stretchdelay {
  0%,
  40%,
  100% {
    transform: scaleY(0.6);
    -webkit-transform: scaleY(0.6);
  }
  20% {
    transform: scaleY(1);
    -webkit-transform: scaleY(1);
  }
}
</style>
