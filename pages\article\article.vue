<script setup>
import { ref, computed } from "vue"
import { onLoad } from "@dcloudio/uni-app"
import request from "/request"
import { useStore } from "/store.js"

const store = useStore()

const headerHeight = computed(() => store.headerHeight)

const articleId = ref(0)
const title = ref("")
const content = ref("")

const loadArticle = async () => {
  const res = await request({
    url: `/mini/article/${articleId.value}`,
    method: "get",
  })
  title.value = res.result.title
  content.value = res.result.content
}

onLoad((options) => {
  articleId.value = options.id
  loadArticle()
})
</script>

<template>
  <navBar :title="title" bgColor="#fff" />

  <view class="page">
    <view class="title">{{ title }}</view>
    <view class="content">
      <mp-html
        :content="content"
        :tag-style="{
          p: 'margin-bottom: 42rpx; font-size: 28rpx; color: #14142b;',
        }"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  padding: 42rpx;
  padding-top: calc(42rpx + v-bind(headerHeight));
}

.title {
  font-size: 44rpx;
  font-weight: bold;
  color: #14142b;
  margin-bottom: 28rpx;
}

.content {
  font-size: 28rpx;
  color: #14142b;
}
</style>
