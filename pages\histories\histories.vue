<script setup>
import { ref, computed } from "vue"
import { onLoad, onShow, onReachBottom } from "@dcloudio/uni-app"
import request from "/request"
import { useStore } from "/store"
import dayjs from "dayjs"
import { sleep } from "/utils"
import { useTTS } from "/pages/index/tts"
import markdownit from "markdown-it"
const md = markdownit()

const store = useStore()
const tts = useTTS()

const headerHeight = computed(() => store.headerHeight)

const searchFocus = ref(false)
const onSearchFocus = () => {
  searchFocus.value = true
}
const onSearchBlur = () => {
  searchFocus.value = false
}

const keyword = ref("")
const datePicker = ref(null)
const timeRange = ref([
  dayjs().startOf("month").format("YYYY-MM-DD"),
  dayjs().endOf("month").format("YYYY-MM-DD"),
])
const handleDateChange = (e) => {
  console.log("handleDateChange", e)
  if (e.type === "tap") {
    e.stopPropagation()
    e.preventDefault()
    clearSearch()
    return
  }
  timeRange.value = e
  refresh()
}
const showDatePicker = () => {
  datePicker.value.show()
}

const nodeTime = ref("")
const list = ref([])
const loadMoreStatus = ref("loadMore") // loadMore, loading, noMore
const loadData = () => {
  request({
    url: "/mini/conversation_history",
    method: "get",
    data: {
      time: nodeTime.value,
      keyword: keyword.value,
      start_at: timeRange.value[0] + " 00:00:00",
      end_at: timeRange.value[1] + " 23:59:59",
    },
  }).then((res) => {
    let temp = []

    const normalList = list.value.filter((item) => item.created_at)
    const last = normalList[normalList.length - 1]

    if (res.result.length) {
      res.result.forEach((item) => {
        let prevTime

        if (temp.length) {
          prevTime = dayjs(temp[temp.length - 1].created_at)
        } else if (last) {
          prevTime = dayjs(last.created_at)
        } else {
          prevTime = dayjs(1)
        }

        const currTime = dayjs(item.created_at)
        if (prevTime.isSame(currTime, "day")) {
          temp.push(item)
        } else {
          temp.push({
            id: prevTime.valueOf(),
            role: "date",
            content: currTime.format("YYYY.MM.DD"),
          })
          temp.push(item)
        }
      })

      nodeTime.value = res.result[res.result.length - 1].created_at
    }

    list.value = list.value.concat(temp)

    if (res.result.length < 10) {
      loadMoreStatus.value = "noMore"
    } else {
      loadMoreStatus.value = "loadMore"
    }
  })
}
const refresh = () => {
  nodeTime.value = ""
  list.value = []
  loadData()
}
const loadMore = () => {
  if (loadMoreStatus.value !== "loadMore") {
    return
  }
  loadData()
}

const onCopy = (message, index) => {
  uni.setClipboardData({
    data: message.content,
    success: () => {
      uni.showToast({
        title: "复制成功",
        icon: "none",
      })
    },
  })
}

const onSpeak = (message, index) => {
  console.log("onSpeak", message, index)
  list.value.forEach((item, i) => {
    if (i !== index) {
      item.voiceStatus = "inactive"
    }
  })

  if (message.voiceStatus === "active") {
    tts.stopSpeak()
    message.voiceStatus = "inactive"
  } else if (!message.voiceStatus || message.voiceStatus === "inactive") {
    message.voiceStatus = "loading"
    tts.startTTS().then(async () => {
      await sleep(1000)
      tts.sendTTSText(message.content)
      tts.endTTS()
    })
    tts.onVoiceStatusChange((e) => {
      message.voiceStatus = e
    })
  }
}

const clearSearch = () => {
  keyword.value = ""
  refresh()
  searchFocus.value = false
}

onLoad(() => {
  refresh()
})
onShow(() => {
  console.log("show")
})
onReachBottom(() => {
  loadMore()
})
</script>

<template>
  <navBar title="聊天记录" bgColor="#f9fbff" />
  <view class="gradient-bg"></view>

  <view
    class="select-bar"
    :style="{
      top: headerHeight,
      marginTop: '-2rpx',
    }"
  >
    <view
      class="select-item date-range"
      v-show="!searchFocus"
      @click="showDatePicker"
    >
      <text>
        {{ dayjs(timeRange[0]).format("MM.DD") }}
      </text>
      <text class="separator">-</text>
      <text>
        {{ dayjs(timeRange[1]).format("MM.DD") }}
      </text>
    </view>
    <view class="select-item type" v-if="false" v-show="!searchFocus">
      <text>类型</text>
      <image class="arrow" src="/static/images/arrow-down.svg" />
    </view>
    <view class="select-item search" :class="{ 'search-focus': searchFocus }">
      <image class="icon" src="/static/images/search.svg" />
      <input
        type="text"
        placeholder="搜索"
        placeholder-class="placeholder"
        :focus="searchFocus"
        @focus="onSearchFocus"
        @blur="onSearchBlur"
        v-model="keyword"
        @confirm="refresh"
      />
      <view class="clear" @click.stop="clearSearch" v-show="keyword">
        <image src="/static/images/close.svg" />
      </view>
    </view>
  </view>

  <view class="page">
    <view class="content">
      <view
        class="message"
        :class="message.role"
        :id="`message-${index}`"
        v-for="(message, index) in list"
        :key="message.id"
      >
        <view
          class="message-content"
          :class="message.role"
          v-if="message.role === 'user'"
        >
          {{ message.content }}
        </view>

        <view v-else-if="message.role === 'date'" class="date">
          {{ message.content }}
        </view>

        <template v-else>
          <view class="message-content" :class="message.role">
            <!-- {{ message.content }} -->
            <mp-html
              :content="md.render(message.content)"
              :tag-style="{
                p: 'margin-bottom: 40rpx;',
                li: 'margin-bottom: 40rpx;',
              }"
            />
            <view class="actions">
              <view class="left">
                <view class="action copy" @click="onCopy(message, index)">
                  <image src="/static/images/answer-action-copy.svg" />
                </view>
                <view class="action speak" @click="onSpeak(message, index)">
                  <image
                    v-if="message.voiceStatus === 'loading'"
                    src="/static/images/answer-action-speak-loading.gif"
                  />
                  <image
                    v-else-if="message.voiceStatus === 'active'"
                    src="/static/images/answer-action-speak-active.gif"
                  />
                  <image v-else src="/static/images/answer-action-speak.svg" />
                </view>
              </view>
            </view>
          </view>
        </template>
      </view>
    </view>

    <loadMoreText :status="loadMoreStatus" />
  </view>

  <uni-datetime-picker
    ref="datePicker"
    type="daterange"
    :value="timeRange"
    @change="handleDateChange"
  >
    <view @click.stop></view>
  </uni-datetime-picker>
</template>

<style>
.placeholder {
  font-size: 32rpx;
  color: #a0a3bd;
}
.uni-date-editor {
  display: flex;
  justify-content: center;
  align-items: center;
}

.uni-calendar--fixed {
  z-index: 100 !important;
  background-color: #ffffff !important;
}
</style>

<style lang="scss" scoped>
.page {
  padding: 24rpx;
  padding-top: calc(176rpx + v-bind(headerHeight));
}

.select-bar {
  position: fixed;
  z-index: 9;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 24rpx;
  background: #f4f5fa;
  box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
  gap: 24rpx;

  .select-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16rpx 36rpx;
    border-radius: 24rpx;
    background: #fff;
    min-width: 218rpx;
    height: 128rpx;
    position: relative;

    text {
      font-size: 28rpx;
      color: #6e7191;
    }

    &.date-range {
      // flex-direction: column;
      line-height: 1;

      text {
        color: #6e7191;
      }

      .separator {
        margin: 0 12rpx;
      }
    }

    &.type {
      text {
        font-size: 32rpx;
        margin-right: 36rpx;
      }

      .arrow {
        width: 16rpx;
        height: 16rpx;
      }
    }

    &.search {
      .icon {
        width: 48rpx;
        height: 48rpx;
      }

      input {
        flex: 1;
        padding: 0 10rpx;
        padding-right: 56rpx;
        font-size: 32rpx;
        text-overflow: ellipsis;
      }

      .clear {
        width: 48rpx;
        height: 48rpx;
        background: rgba(235, 240, 247, 0.87);
        border-radius: 24rpx;
        display: flex;
        justify-content: center;
        align-items: center;

        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;

        image {
          width: 28rpx;
          height: 28rpx;
        }
      }
    }

    &.search-focus {
      input {
        padding: 0 32rpx;
      }
    }
  }
}

.message {
  padding: 12rpx 0;
  display: flex;

  &.user {
    padding-left: 42rpx;
    justify-content: flex-end;
  }
}

.message-content {
  min-width: 150rpx;
  font-size: 34rpx;
  line-height: 54rpx;
  padding: 30rpx;
  text-align: justify;

  &.user {
    background: #466fff;
    border-radius: 48rpx 48rpx 12rpx 48rpx;
    color: #ffffff;
    width: fit-content;
    max-width: 100%;
  }

  &.artificial,
  &.assistant {
    width: 100%;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
    padding: 30rpx;
    border-radius: 48rpx 48rpx 12rpx 48rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.5);
    color: #253a57;

    .actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-top: 40rpx;

      .left,
      .right {
        display: flex;
        align-items: center;
        gap: 24rpx;

        .action {
          width: 80rpx;
          height: 80rpx;
          border-radius: 24rpx 24rpx 24rpx 24rpx;
          background: rgba(70, 111, 255, 0.06);
          display: flex;
          justify-content: center;
          align-items: center;

          image {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }
    }
  }
}

.date {
  padding-top: 30rpx;
  padding-bottom: 20rpx;
  font-weight: bold;
  font-size: 56rpx;
  color: #253a57;
}
</style>
