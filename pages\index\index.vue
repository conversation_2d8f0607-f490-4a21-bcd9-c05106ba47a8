<script setup>
import { ref, watch, computed, provide, nextTick, onUnmounted } from "vue"
import {
  onLoad,
  onShow,
  onShareAppMessage,
  onShareTimeline,
} from "@dcloudio/uni-app"
import { sleep } from "/utils"
import { useStore } from "/store"
import request from "/request"
import MySocket from "./MySocket"
import { useTTS } from "./tts"
import { useST } from "./st"

const store = useStore()

// 语音相关状态
const tts = ref(null)
const st = ref(null)
const isMute = ref(false)

// 消息相关状态
const isGenerating = ref(false)
const messagesList = ref([])
const chatMode = ref("assistant")

// 语音队列相关
let msgContentQueue = []
let speakFlag = false

// 弹窗状态
const rechargePopupVisible = ref(false)
const feedBackVisible = ref(false)
const serviceVisible = ref(false)
const completeInfoPopupVisible = ref(false)
const callVisible = ref(false)
const callStatus = ref("")

// 评价相关
const toRateAnswerInstance = ref(null)
const defaultRateAnswer = ref(null)

// 计算属性
const headerHeight = computed(() => store.headerHeight)
const leftTimes = computed(() => store.leftTimes)

// 消息处理
const handleSpeakRealTime = async () => {
  try {
    if (msgContentQueue.length === 0) {
      const lastMessage = messagesList.value[messagesList.value.length - 1]
      if (lastMessage?.status !== "done") {
        await sleep(300)
        handleSpeakRealTime()
      } else {
        msgContentQueue = []
        speakFlag = false
        tts.value?.stopTTS(false)
      }
      return
    }
    const text = msgContentQueue.shift()
    if (text && text.trim()) {
      await tts.value?.sendTTSText(text)
      await sleep(100)
    }
    handleSpeakRealTime()
  } catch (error) {
    console.error("Error in handleSpeakRealTime:", error)
  }
}

// 语音监听
const listenTTS = () => {
  tts.value?.onTTSChange(async (name, msg) => {
    console.log("tts.value?.onTTSChange", name, msg)
    if (name === "failed" && callVisible.value) {
      callStatus.value = ""
      await st.value?.stopST()
      st.value?.startST()
    }
  })
}

const listenSt = () => {
  st.value?.onStChange(async (name, msg) => {
    console.log("st.value?.onStChange", name, msg)

    switch (name) {
      case "started":
        callStatus.value = "listen"
        uni.hideLoading({ noConflict: true })
        break

      case "end":
        st.value?.stopST()
        const text = msg.payload.result
        if (text) {
          onConfirm(text)
            .then(() => {
              callStatus.value = "think"
            })
            .catch(() => {
              setTimeout(() => {
                st.value?.startST()
              }, 1000)
            })
        }
        break

      case "failed":
        if (callStatus.value === "listen") {
          callStatus.value = ""
          await st.value?.stopST()
          st.value?.startST()
        }
        break
    }
  })
}

// Socket消息处理
const onMessage = async (res) => {
  try {
    const { action, data, content_type } = res.result

    if (action === "reply") {
      if (data.session_id) {
        store.setSessionId(data.session_id)
      }

      const lastMessage = messagesList.value[messagesList.value.length - 1]

      if (chatMode.value === "artificial") {
        messagesList.value.push({
          role: "artificial",
          content: data.content,
          status: "done",
        })
      } else if (chatMode.value === "assistant") {
        if (lastMessage.status === "done") {
          return
        }
        lastMessage.status = "output"
        if (content_type === "text") {
          lastMessage.content += data.content
        } else if (content_type === "file") {
          const attachment_id = data.content
          const attachment = await request({
            url: `/mini/get_file?files_id=${attachment_id}`,
            method: "GET",
          })
          if (attachment.code === 200) {
            lastMessage.res_attachment = attachment.result
          }
        }
      }

      if ((callVisible.value || !isMute.value) && data.content.trim()) {
        msgContentQueue.push(data.content)
        if (!speakFlag) {
          speakFlag = true
          tts.value?.startTTS()
          await sleep(500)
          handleSpeakRealTime()
        }
      }

      if (data.finish_reason === "stop") {
        store.minusLeftTimes()
        isGenerating.value = false
        lastMessage.status = "done"
        lastMessage.record_id = data.record_id
      }
    }
  } catch (error) {
    console.error("Error in onMessage:", error)
  }
}

const mySocket = new MySocket({ onMessage })

// 消息发送
const onConfirm = (text, ability, attachment = null) => {
  if (isGenerating.value) {
    abort()
  } else {
    tts.value?.stopTTS()
  }

  return new Promise((resolve, reject) => {
    if (!mySocket?.isConnecting) {
      reject("socket is not connecting")
    }

    const data = {
      action: "question",
      prompt: text,
    }
    if (ability) {
      data.ability = ability
    }
    if (attachment) {
      data.attachment_id = attachment.id
    }

    mySocket
      .send(data)
      .then(() => {
        resolve()
        messagesList.value.push({
          role: "user",
          content: text,
          attachment: attachment,
        })

        if (chatMode.value === "assistant") {
          messagesList.value.push({
            role: "assistant",
            content: "",
            status: "generating",
          })
          isGenerating.value = true

          if (callVisible.value || !isMute.value) {
            const msg = messagesList.value[messagesList.value.length - 1]
            msg.voiceStatus = "loading"

            tts.value?.onVoiceStatusChange((e) => {
              console.log("onVoiceStatusChange", e)
              handleVoiceStatusChange(e, msg)
            })
          }
        }

        msgContentQueue = []
        speakFlag = false
      })
      .catch(async (err) => {
        reject(err)
        console.error("onConfirm", err)
        uni.showToast({
          title: "发送失败, 请重试",
          icon: "none",
        })
        if (callVisible.value) {
          callStatus.value = ""
          await st.value?.stopST()
          st.value?.startST()
        }
      })
  })
}

// 语音状态变化处理
const handleVoiceStatusChange = (status, msg) => {
  switch (status) {
    case "play":
      msg.voiceStatus = "active"
      if (callVisible.value) {
        callStatus.value = "speak"
      }
      break
    case "pause":
    case "stop":
      msg.voiceStatus = "inactive"
      break
    case "complete":
    case "error":
      console.log("complete or error", msg)
      msg.voiceStatus = "inactive"
      if (callVisible.value) {
        st.value?.startST()
      }
      break
    default:
      break
  }
}

// 通话相关
const showCall = () => {
  if (isGenerating.value) {
    abort()
  } else {
    tts.value?.stopTTS()
  }

  uni.showLoading({
    title: "连接中...",
    mask: true,
  })

  callVisible.value = true
  listenSt()
  st.value?.startST()
  wx.setKeepScreenOn({
    keepScreenOn: true,
  })
}

const exitCall = () => {
  callVisible.value = false
  wx.setKeepScreenOn({
    keepScreenOn: false,
  })
  st.value?.stopST()
  tts.value?.stopTTS()
}

let callStatusBeforePause = ""

const callPause = () => {
  if (callStatus.value === "speak") {
    tts.value?.pauseTTS()
  } else {
    abort()
  }
  st.value?.stopST()
  callStatusBeforePause = callStatus.value
  callStatus.value = "pause"
}

const callResume = () => {
  if (callStatusBeforePause === "speak") {
    tts.value?.resumeTTS()
  } else {
    st.value?.startST()
  }
}

const callAbort = async () => {
  callStatus.value = ""
  abort()
  await st.value?.stopST()
  st.value?.startST()
}

// 评价相关
const toRateAnswer = async (message) => {
  console.log("toRateAnswer", message)
  toRateAnswerInstance.value = message
  try {
    const res = await request({
      url: `/mini/conversation_record/${message.record_id}/assessment`,
      method: "GET",
    })
    if (res?.code === 200) {
      defaultRateAnswer.value = res.result
    }
    feedBackVisible.value = true
  } catch (error) {
    console.error("Error in toRateAnswer:", error)
  }
}

const onConfirmRate = async (data) => {
  try {
    const res = await request({
      url: `/mini/conversation_record/${toRateAnswerInstance.value.record_id}/assessment`,
      method: "POST",
      data,
    })
    if (res.code === 200) {
      feedBackVisible.value = false
      defaultRateAnswer.value = null
      uni.showToast({
        title: "评价成功",
        icon: "success",
      })
    }
  } catch (error) {
    console.error("Error in onConfirmRate:", error)
  }
}

// 中止生成
const abort = () => {
  request({
    url: "/mini/conversation_stop",
    method: "POST",
    hideLoading: true,
  })
  const lastMessage = messagesList.value[messagesList.value.length - 1]
  if (lastMessage) {
    if (lastMessage.status === "generating") {
      messagesList.value.pop()
    } else if (lastMessage.status === "output") {
      lastMessage.status = "done"
      lastMessage.voiceStatus = "inactive"
    }
  }

  msgContentQueue = []
  speakFlag = false
  tts.value?.stopTTS()
  isGenerating.value = false
  store.getUserInfo()
}

// 重新生成和重新播放
const reGenerate = () => {
  tts.value?.stopTTS()
  messagesList.value.pop()
  const lastMessage = messagesList.value[messagesList.value.length - 1]
  const text = lastMessage.content
  messagesList.value.pop()
  onConfirm(text)
}

const reSpeak = async (message) => {
  if (message.voiceStatus === "active") {
    tts.value?.stopTTS()
    message.voiceStatus = "inactive"
  } else if (!message.voiceStatus || message.voiceStatus === "inactive") {
    message.voiceStatus = "loading"
    await tts.value?.stopTTS()
    tts.value?.startTTS()
    await sleep(1000)
    await tts.value?.sendTTSText(message.content)

    tts.value?.onVoiceStatusChange((e) => {
      switch (e) {
        case "start":
        case "play":
          message.voiceStatus = "active"
          break
        case "pause":
        case "stop":
        case "complete":
        case "error":
          message.voiceStatus = "inactive"
          break
      }
    })
  }
}

// 聊天模式切换
const toChangeChatMode = (mode) => {
  if (mode === chatMode.value) return

  if (mode === "artificial") {
    serviceVisible.value = true
  } else {
    uni.showModal({
      title: "提示",
      content: "是否退出人工服务？",
      success: async (res) => {
        if (res.confirm) {
          try {
            const res1 = await request({
              url: `/mini/conversation_setting`,
              method: "POST",
              data: {
                mode: "assistant",
                type: "consult",
              },
            })
            if (res1.code === 200) {
              chatMode.value = "assistant"
              messagesList.value.push({
                role: "manual",
                content: "",
                status: "exited",
              })
            }
          } catch (error) {
            console.error("Error in toChangeChatMode:", error)
          }
        }
      },
    })
  }
}

const confirmService = async () => {
  try {
    const res = await request({
      url: `/mini/conversation_setting`,
      method: "POST",
      data: {
        mode: "artificial",
        type: "consult",
      },
    })
    if (res.code === 200) {
      chatMode.value = "artificial"
      serviceVisible.value = false
      messagesList.value.push({
        role: "manual",
        content: "",
        status: "entered",
      })
    }
  } catch (error) {
    console.error("Error in confirmService:", error)
  }
}

// 其他功能
const toggleMute = () => {
  isMute.value = !isMute.value
  if (isMute.value) {
    tts.value?.stopTTS()
  }
}

const messageRef = ref(null)
const onInputFocus = () => {
  messageRef.value.scrollIntoBottom()
}
const onInputBlur = () => {
  messageRef.value.updateScrollTop("lower")
}

const checkAvailability = () => {
  if (store.isLogin && !store.isCompleteUserInfo) {
    completeInfoPopupVisible.value = true
  } else if (leftTimes.value <= 0) {
    if (!store.paySwitch) return
    rechargePopupVisible.value = true
  }
}

const goCompleteInfo = () => {
  uni.navigateTo({
    url: "/pages/profile/profile",
  })
  completeInfoPopupVisible.value = false
}

// 初始化
const init = async () => {
  try {
    mySocket.init()
    const res = await request({
      url: `/mini/conversation_setting`,
      method: "POST",
      data: {
        mode: "assistant",
        type: "consult",
      },
    })
    if (res.code === 200) {
      chatMode.value = "assistant"
    }
    await store.fetchNlsToken()
    tts.value = useTTS()
    st.value = useST()
    listenTTS()
    
    // 应用启动时检查并清理存储空间
    if (tts.value?.checkStorageSpace) {
      const shouldCleanup = await tts.value.checkStorageSpace()
      if (shouldCleanup && tts.value?.cleanupAllFiles) {
        await tts.value.cleanupAllFiles()
      }
    }
  } catch (error) {
    console.error("Error in init:", error)
  }
}

// 生命周期
watch(
  () => store.isLogin,
  (value) => {
    if (value) {
      init()
    }
  },
  { immediate: true }
)

onLoad(() => {
  console.log("index onLoad")
})

onShow(() => {
  console.log("index onShow")
})

provide("st", st)

// 分享
onShareAppMessage((res) => {
  return {
    title: store.inviteShareTitle,
    imageUrl: store.inviteShareImage,
    path: `/pages/index/index`,
  }
})

onShareTimeline(() => {})

onUnmounted(() => {
  // 清理语音识别资源
  st.value?.cleanup?.()
  // 停止语音合成
  tts.value?.stopTTS()
  // 关闭 socket 连接
  mySocket?.close()
})
</script>

<template>
  <image src="/static/images/index-bg.png" class="index-bg" />
  <indexHeader
    :leftTimes="leftTimes"
    :chatMode="chatMode"
    :isMute="isMute"
    @toggleMute="toggleMute"
  />
  <bottomPanel
    :chatMode="chatMode"
    :isGenerating="isGenerating"
    :isMute="isMute"
    :disabled="!store.accountAvailability"
    @toggleMute="toggleMute"
    @confirm="onConfirm"
    @exitArtificial="toChangeChatMode('assistant')"
    @call="showCall"
    @abort="abort"
    @inputFocus="onInputFocus"
    @inputBlur="onInputBlur"
    @click="checkAvailability"
  />

  <view class="index-content" @click="onContentClick">
    <messages
      ref="messageRef"
      v-if="store.isLogin"
      :list="messagesList"
      :isMute="isMute"
      @manual="toChangeChatMode('artificial')"
      @rate="toRateAnswer"
      @refresh="reGenerate"
      @speak="reSpeak"
    />
  </view>

  <call
    :visible="callVisible"
    :callStatus="callStatus"
    @pause="callPause"
    @resume="callResume"
    @abort="callAbort"
    @exit="exitCall"
  />

  <rechargePopup v-model:visible="rechargePopupVisible" />
  <feedBackPopup
    v-model:visible="feedBackVisible"
    :defaultData="defaultRateAnswer"
    @confirm="onConfirmRate"
  />
  <servicePopup v-model:visible="serviceVisible" @confirm="confirmService" />

  <completeInfoPopup
    v-model:visible="completeInfoPopupVisible"
    @confirm="goCompleteInfo"
  />

  <!-- <privacyPopup /> -->
</template>

<style lang="scss" scoped>
.index-bg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
}

.index-content {
  position: absolute;
  top: v-bind(headerHeight);
  left: 0;
  z-index: 0;
  width: 100vw;
  height: calc(100vh - v-bind(headerHeight));
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  overflow: hidden;
}
</style>
