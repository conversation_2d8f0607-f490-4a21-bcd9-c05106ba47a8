import SpeechTranscription from "/utils/st"
import { sleep } from "/utils"
import { useStore } from "/store"

const store = useStore()

let recordManager = wx.getRecorderManager()
let st

let changeCallback

const init = async () => {
  if (!store.nlsToken) {
    await store.fetchNlsToken()
  }

  recordManager.onFrameRecorded((res) => {
    if (res.isLastFrame) {
      console.log("record done")
    }
    if (res.frameBuffer) {
      console.log("send " + res.frameBuffer.byteLength)
      st?.sendAudio(res.frameBuffer)
    }
  })
  recordManager.onStart(() => {
    console.log("start recording...")
  })
  recordManager.onStop((res) => {
    console.log("stop recording...")
    if (res.tempFilePath) {
      wx.getFileSystemManager().removeSavedFile({
        filePath: res.tempFilePath,
      })
    }
  })
  recordManager.onError((res) => {
    console.log("recording failed:" + res)
  })

  st = new SpeechTranscription({
    url: store.nlsUrl,
    appkey: store.nlsAppKey,
    token: store.nlsToken,
  })

  st.on("started", (msg) => {
    console.log("Client recv started", msg)
    changeCallback && changeCallback("started", JSON.parse(msg))
  })

  st.on("changed", (msg) => {
    console.log("Client recv changed:", msg)
    changeCallback && changeCallback("changed", JSON.parse(msg))
  })

  st.on("completed", (msg) => {
    console.log("Client recv completed:", msg)
    changeCallback && changeCallback("completed", JSON.parse(msg))
  })

  st.on("begin", (msg) => {
    console.log("Client recv sentenceBegin:", msg)
    changeCallback && changeCallback("begin", JSON.parse(msg))
  })

  st.on("end", (msg) => {
    console.log("Client recv sentenceEnd:", msg)
    changeCallback && changeCallback("end", JSON.parse(msg))
  })

  st.on("closed", () => {
    console.log("Client recv closed")
    recordManager.stop()
    changeCallback && changeCallback("closed")
  })

  st.on("failed", (msg) => {
    console.log("Client recv failed:", msg)
    changeCallback && changeCallback("failed", msg)
  })
}

export const useST = () => {
  init()

  const startST = async () => {
    if (!st) {
      await init()
    }
    await st.start(
      Object.assign(st.defaultStartParams(), {
        format: "PCM",
        sample_rate: 8000,
        // max_sentence_silence: 1200, //语音断句检测阈值
        enable_intermediate_result: false, //是否返回中间识别结果
        enable_semantic_sentence_detection: true, //是否开启语义断句
      })
    )

    recordManager.start({
      duration: 600000,
      numberOfChannels: 1,
      sampleRate: 8000,
      format: "PCM",
      frameSize: 4,
    })
  }

  const stopST = () => {
    return new Promise(async (resolve, reject) => {
      recordManager.stop()
      await sleep(500)
      try {
        await st.close()
      } catch (e) {
        console.log("stop failed", e)
      }
      resolve()
    })
  }

  const onStChange = (callback) => {
    changeCallback = callback
  }

  // 添加资源释放
  const cleanup = () => {
    if (recordManager) {
      recordManager.stop()
    }
    if (st) {
      st.shutdown()
      st = null
    }
    changeCallback = null
  }

  // 添加错误重试
  const startSTWithRetry = async (maxRetries = 2) => {
    let retryCount = 0
    
    while (retryCount <= maxRetries) {
      try {
        // 确保之前的实例被清理
        if (st) {
          await stopST()
          st = null
        }
        
        await init()
        await startST()
        return
      } catch (err) {
        console.error(`语音识别启动失败，重试次数: ${retryCount}`, err)
        retryCount++
        if (retryCount > maxRetries) {
          throw new Error(`语音识别启动失败，已重试${maxRetries}次: ${err.message}`)
        }
        await sleep(1000 * retryCount)
      }
    }
  }

  return {
    startST: startSTWithRetry,
    stopST,
    onStChange,
    cleanup // 导出清理方法
  }
}
