import SpeechSynthesizer from "/utils/tts"
import { sleep, getRandomString } from "/utils"
import { useStore } from "/store"

const store = useStore()
const fs = wx.getFileSystemManager()

let tts
let audioCtx
let queue = []
let handleCount = 0
let fileQueue = []
let allDoneFlag = false
let changeCallback, onVoiceStatusChangeCallback
let voiceStatus = ""

const handleFileQueue = async (fileQueue) => {
  const file = fileQueue.shift()
  try {
    await playFile(file)
    if (
      allDoneFlag &&
      fileQueue.indexOf(file) === fileQueue.length - 1 &&
      voiceStatus !== "complete"
    ) {
      voiceStatus = "complete"
      onVoiceStatusChangeCallback?.("complete")
    } else {
      handleFileQueue(fileQueue)
    }
  } catch (err) {
    console.error("播放失败:", err)
    // 播放失败时立即清理当前文件
    cleanupFile(file)
    if (fileQueue.length) {
      handleFileQueue(fileQueue)
    } else {
      voiceStatus = "error"
      onVoiceStatusChangeCallback?.("error")
    }
  }
}

const playFile = (file) => {
  return new Promise((resolve, reject) => {
    audioCtx = wx.createInnerAudioContext({
      useWebAudioImplement: true,
    })
    audioCtx.src = file
    audioCtx.autoplay = voiceStatus !== "pause"

    const timeoutId = setTimeout(() => {
      console.log("播放超时，尝试重新播放")
      audioCtx?.destroy()
      reject(new Error("播放超时"))
    }, 1000)

    const updateStatus = (status) => {
      voiceStatus = status
      onVoiceStatusChangeCallback?.(status)
    }

    audioCtx.onPlay(() => {
      clearTimeout(timeoutId)
      console.log("开始播放", file)
      updateStatus("play")
    })

    audioCtx.onEnded(() => {
      clearTimeout(timeoutId)
      console.log("播放结束")
      cleanupAudio(file)
      resolve()
    })

    audioCtx.onError((res) => {
      console.error("播放失败", res)
      cleanupAudio(file)
      reject(res)
    })

    audioCtx.onStop(() => {
      console.log("停止播放")
      updateStatus("stop")
      cleanupAudio(file)
    })

    audioCtx.onPause(() => {
      console.log("暂停播放")
      updateStatus("pause")
    })
  })
}

// 清理单个文件
const cleanupFile = (filePath) => {
  try {
    fs.unlink({
      filePath,
      fail: (err) => console.error("删除文件失败", err),
    })
  } catch (error) {
    console.error("清理文件失败", error)
  }
}

// 检查存储空间使用情况
const checkStorageSpace = async () => {
  try {
    const storageInfo = await new Promise((resolve) => {
      wx.getStorageInfo({
        success: (res) => resolve(res),
        fail: () => resolve({ currentSize: 0, limitSize: 10240 }) // 默认10MB
      })
    })
    
    const usagePercent = (storageInfo.currentSize / storageInfo.limitSize) * 100
    console.log(`存储使用率: ${usagePercent.toFixed(2)}% (${storageInfo.currentSize}KB / ${storageInfo.limitSize}KB)`)
    
    // 如果使用率超过80%，建议清理
    if (usagePercent > 80) {
      console.log("存储使用率过高，建议清理文件")
      return true
    }
    return false
  } catch (error) {
    console.error("检查存储空间失败", error)
    return false
  }
}

// 清理所有TTS相关文件
const cleanupAllFiles = async () => {
  try {
    // 清理当前队列中的文件
    await Promise.all(
      fileQueue.map(
        (file) =>
          new Promise((resolve) => {
            fs.unlink({
              filePath: file,
              complete: resolve,
            })
          })
      )
    )
    fileQueue = []
    
    // 清理用户数据目录下的所有mp3文件
    const files = await new Promise((resolve) => {
      fs.readdir({
        dirPath: wx.env.USER_DATA_PATH,
        success: (res) => resolve(res.files || []),
        fail: () => resolve([])
      })
    })
    
    const mp3Files = files.filter(file => file.endsWith('.mp3'))
    await Promise.all(
      mp3Files.map(
        (file) =>
          new Promise((resolve) => {
            fs.unlink({
              filePath: `${wx.env.USER_DATA_PATH}/${file}`,
              complete: resolve,
            })
          })
      )
    )
    
    console.log(`清理了 ${mp3Files.length} 个音频文件`)
    
    // 清理后再次检查存储空间
    await checkStorageSpace()
  } catch (error) {
    console.error("清理所有文件失败", error)
  }
}

const cleanupAudio = (file) => {
  try {
    audioCtx?.destroy()
    cleanupFile(file)
  } catch (error) {
    console.error("清理资源失败", error)
  }
}

const handleQueue = (_handleCount) => {
  const dataArr = queue[_handleCount]

  if (!dataArr?.length) {
    console.log("当前数据段为空，跳过处理")
    return
  }

  const filePath = `${wx.env.USER_DATA_PATH}/${getRandomString(32)}.mp3`

  fs.open({
    filePath,
    flag: "a+",
    success: (res) => {
      try {
        dataArr.forEach((data, index) => {
          try {
            fs.appendFileSync(filePath, data, "binary")
          } catch (appendError) {
            console.error("appendFileSync失败:", appendError)
            // 如果是存储空间不足错误，先清理再重试
            if (appendError.errMsg && appendError.errMsg.includes('maximum size')) {
              console.log("检测到存储空间不足，开始清理...")
              cleanupAllFiles().then(() => {
                try {
                  fs.appendFileSync(filePath, data, "binary")
                } catch (retryError) {
                  console.error("清理后重试仍然失败:", retryError)
                  throw retryError
                }
              })
            } else {
              throw appendError
            }
          }

          if (index === dataArr.length - 1) {
            fs.close({
              fd: res.fd,
              success: () => {
                fileQueue.push(filePath)
                if (_handleCount === 0) {
                  handleFileQueue(fileQueue)
                  voiceStatus = "start"
                  onVoiceStatusChangeCallback?.("start")
                }
              },
              fail: (err) => {
                console.error("关闭文件失败:", err)
                // 关闭失败时也要清理文件
                cleanupFile(filePath)
              },
            })
          }
        })
      } catch (error) {
        console.error("写入文件失败:", error)
        // 写入失败时清理文件
        fs.close({
          fd: res.fd,
          complete: () => cleanupFile(filePath)
        })
      }
    },
    fail: (err) => {
      console.error("打开文件失败:", err)
    },
  })
}

const init = async () => {
  if (!store.nlsToken) {
    await store.fetchNlsToken()
  }

  tts = new SpeechSynthesizer({
    url: store.nlsUrl,
    appkey: store.nlsAppKey,
    token: store.nlsToken,
  })

  const setupEventHandler = (event, handler) => {
    tts.on(event, handler)
  }

  setupEventHandler("data", (msg) => {
    // console.log("Client recv data:", JSON.stringify(msg))
    if (!queue[queue.length - 1]) {
      queue[queue.length - 1] = []
    }
    queue[queue.length - 1].push(msg)
  })

  setupEventHandler("started", (msg) => {
    console.log("Client recv started", msg)
    changeCallback?.("started", JSON.parse(msg))
  })

  setupEventHandler("completed", async (msg) => {
    console.log("Client recv completed:", msg)
    allDoneFlag = true
    changeCallback?.("completed", JSON.parse(msg))
  })

  setupEventHandler("begin", (msg) => {
    console.log("Client recv begin:", msg)
    queue.push([])
    changeCallback?.("begin", JSON.parse(msg))
  })

  setupEventHandler("end", (msg) => {
    console.log("Client recv end:", msg)
    changeCallback?.("end", JSON.parse(msg))
    handleQueue(handleCount)
    handleCount++
  })

  setupEventHandler("closed", () => {
    console.log("Client recv closed")
    changeCallback?.("closed")
  })

  setupEventHandler("failed", (msg) => {
    console.log("Client recv failed:", msg)
    allDoneFlag = true
    changeCallback?.("failed", JSON.parse(msg))
  })
}

export const useTTS = () => {
  init()

  const startTTS = async () => {
    if (!tts) {
      await init()
    }

    // 检查存储空间，如果使用率过高则清理
    const shouldCleanup = await checkStorageSpace()
    if (shouldCleanup) {
      await cleanupAllFiles()
    }

    queue = []
    handleCount = 0
    fileQueue = []
    allDoneFlag = false

    await tts.start(
      Object.assign(tts.defaultStartParams(), {
        voice: "zhiyuan",
        format: "mp3",
      })
    )
  }

  const sendTTSText = async (text) => {
    const cleanText = text.replace(/[#`*]/g, "")
    tts.send(cleanText)
  }

  const stopTTS = async (stopAudio = true) => {
    if (stopAudio) {
      audioCtx?.stop()
      // 使用统一的清理方法
      await cleanupAllFiles()
    }

    try {
      await tts?.close()
    } catch (e) {
      console.log("stop failed", e)
    }
  }

  const onTTSChange = (callback) => {
    changeCallback = callback
  }

  const pauseTTS = () => {
    audioCtx?.pause()
  }

  const resumeTTS = () => {
    audioCtx?.play()
  }

  const onVoiceStatusChange = (callback) => {
    onVoiceStatusChangeCallback = callback
  }

  const getVoiceStatus = () => voiceStatus

  return {
    startTTS,
    sendTTSText,
    stopTTS,
    pauseTTS,
    resumeTTS,
    onTTSChange,
    onVoiceStatusChange,
    getVoiceStatus,
    cleanupAllFiles, // 导出清理方法供外部调用
    checkStorageSpace, // 导出存储检查方法
  }
}
