<script setup>
import { ref, computed } from "vue"
import { useStore } from "/store"

const store = useStore()
const userInfo = computed(() => store.userInfo)

const menuList = computed(() => [
  {
    icon: "/static/images/mine-icon1.svg",
    title: "我的积分",
    value: store.points,
    path: "/pages/myPoints/myPoints",
  },
  {
    icon: "/static/images/mine-icon2.svg",
    title: "聊天记录",
    path: "/pages/histories/histories",
  },
  {
    icon: "/static/images/mine-icon3.svg",
    title: "充值",
    path: "/pages/recharge/recharge",
    isShow: store.paySwitch,
  },
  {
    icon: "/static/images/mine-icon4.svg",
    title: "我的资料",
    path: "/pages/profile/profile",
  },
  {
    icon: "/static/images/mine-icon5.svg",
    title: "更多",
    path: "/pages/more/more",
  },
])

const goPage = (path) => {
  uni.navigateTo({ url: path })
}
</script>

<template>
  <navBar title="我的" />
  <view class="gradient-bg"></view>
  <view class="page">
    <view class="info">
      <view class="user">
        <image class="avatar" :src="store.avatar" mode="aspectFill" />
        <view class="text">
          <view class="name">{{ userInfo.name }}</view>
          <view class="limit">剩余次数：{{ userInfo.left_times }}</view>
        </view>
      </view>
      <view
        class="notice-icon"
        @click="goPage('/pages/notices/notices')"
        v-if="false"
      >
        <image src="/static/images/bell.svg" />
        <view class="point"></view>
      </view>
    </view>
    <view class="menu">
      <view
        class="menu-item"
        v-for="(item, index) in menuList.filter(
          (item) => item.isShow !== false
        )"
        :key="index"
        @click="goPage(item.path)"
      >
        <view class="left">
          <image :src="item.icon" />
          <text>
            {{ item.title }}
          </text>
        </view>
        <view class="right">
          <text class="value">
            {{ item.value }}
          </text>
          <image src="/static/images/arrow.svg" />
        </view>
      </view>
    </view>
  </view>
  <copyRight />
</template>

<style lang="scss" scoped>
.page {
  padding-top: 200rpx;
}

.info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;

  .user {
    display: flex;
    align-items: center;

    .avatar {
      width: 96rpx;
      height: 96rpx;
      border-radius: 50%;
      background-color: #f5f5f5;
    }

    .text {
      margin-left: 24rpx;

      .name {
        font-size: 36rpx;
        font-weight: bold;
        color: #253a57;
      }

      .limit {
        font-size: 24rpx;
        color: #6e7191;
        margin-top: 16rpx;
      }
    }
  }

  .notice-icon {
    position: relative;
    width: 80rpx;
    height: 80rpx;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    box-shadow: 0rpx 32rpx 48rpx 0rpx rgba(160, 163, 189, 0.16);
    border-radius: 108rpx 108rpx 108rpx 108rpx;
    border: 2rpx solid rgba(252, 252, 252, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;

    image {
      width: 48rpx;
      height: 48rpx;
    }

    .point {
      width: 16rpx;
      height: 16rpx;
      background: #ff4141;
      border-radius: 50%;
      position: absolute;
      top: 16rpx;
      right: 16rpx;
    }
  }
}

.menu {
  width: 670rpx;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );
  box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
  border-radius: 48rpx 48rpx 48rpx 48rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  margin: 40rpx auto;
  padding: 24rpx 0;

  .menu-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 38rpx 32rpx;

    .left {
      display: flex;
      align-items: center;

      image {
        width: 48rpx;
        height: 48rpx;
      }

      text {
        line-height: 1;
        font-weight: 500;
        font-size: 32rpx;
        color: #253a57;
        margin-left: 24rpx;
      }
    }

    .right {
      display: flex;
      align-items: center;

      .value {
        font-size: 32rpx;
        color: #6e7191;
        margin-right: 12rpx;
        line-height: 1;
      }

      image {
        width: 14rpx;
        height: 23rpx;
      }
    }
  }
}
</style>
