<script setup>
import { ref } from "vue"
import { useStore } from "/store"

const store = useStore()

const showDialog = ref(false)

const menuList = [
  {
    icon: "/static/images/more-icon1.svg",
    title: "用户协议",
    path: `/pages/article/article?id=${store.agreementArticleId}`,
  },
  {
    icon: "/static/images/more-icon2.svg",
    title: "隐私政策",
    // handle: () => {
    //   wx.openPrivacyContract()
    // },
    path: `/pages/article/article?id=${store.privacyArticleId}`,
  },
  {
    icon: "/static/images/more-icon3.svg",
    title: "意见反馈",
    openType: "feedback",
  },
  // {
  //   icon: "/static/images/more-icon4.svg",
  //   title: "退出登录",
  //   handle: () => {
  //     showDialog.value = true
  //   },
  // },
]

const confirmLogout = () => {
  console.log("退出登录")
  showDialog.value = false
  store.logOut()
  uni.reLaunch({
    url: "/pages/index/index",
  })
}

const onNav = (item) => {
  if (item.path) {
    uni.navigateTo({
      url: item.path,
    })
  } else if (item.handle) {
    item.handle()
  }
}
</script>

<template>
  <navBar title="更多" />
  <view class="gradient-bg"></view>

  <view class="page">
    <view class="menu">
      <button
        class="menu-item"
        v-for="(item, index) in menuList"
        :key="index"
        :open-type="item.openType || ''"
        @click="onNav(item)"
      >
        <view class="left">
          <image :src="item.icon" />
          <text>
            {{ item.title }}
          </text>
        </view>
        <view class="right">
          <image src="/static/images/arrow.svg" />
        </view>
      </button>
    </view>
  </view>

  <copyRight />

  <myDialog
    v-model:show="showDialog"
    title="确定要退出登录吗？"
    @confirm="confirmLogout"
    confirmText="退出登录"
    cancelText="不了"
  />
</template>

<style lang="scss" scoped>
.page {
  padding-top: 200rpx;
}

.menu {
  width: 670rpx;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );
  box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
  border-radius: 48rpx 48rpx 48rpx 48rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  margin: 40rpx auto;
  padding: 24rpx 0;

  .menu-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 38rpx 32rpx;

    .left {
      display: flex;
      align-items: center;

      image {
        width: 48rpx;
        height: 48rpx;
      }

      text {
        line-height: 1;
        font-weight: 500;
        font-size: 32rpx;
        color: #253a57;
        margin-left: 24rpx;
      }
    }

    .right {
      display: flex;
      align-items: center;

      .value {
        font-size: 32rpx;
        color: #6e7191;
        margin-right: 12rpx;
        line-height: 1;
      }

      image {
        width: 14rpx;
        height: 23rpx;
      }
    }
  }
}
</style>
