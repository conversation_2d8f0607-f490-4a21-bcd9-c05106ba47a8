<script setup>
import { ref, computed } from "vue"
import {
  onLoad,
  onShow,
  onReachBottom,
  onShareAppMessage,
} from "@dcloudio/uni-app"
import request from "/request"
import { useStore } from "/store"

const store = useStore()

const headerHeight = computed(() => store.headerHeight)

const page = ref(1)
const pageSize = 10
const list = ref([])
const loadMoreStatus = ref("loadMore") // loadMore, loading, noMore
const loadData = () => {
  request({
    url: "/mini/point_record",
    method: "get",
    data: {
      page: page.value,
      page_size: pageSize,
    },
  }).then((res) => {
    list.value = list.value.concat(res.result?.data || [])
    if (res.result?.data?.length < 10) {
      loadMoreStatus.value = "noMore"
    } else {
      loadMoreStatus.value = "loadMore"
    }
  })
}
const refresh = () => {
  page.value = 1
  list.value = []
  loadData()
}
const loadMore = () => {
  if (loadMoreStatus.value !== "loadMore") {
    return
  }
  page.value++
  loadData()
}

const timeToPointsRate = computed(() => store.timeToPointsRate)
const myPoints = computed(() => store.points)
const times = ref(10)
const spendPoints = computed(() => {
  return times.value * timeToPointsRate.value
})
const addTimes = (num) => {
  times.value += num
  if (times.value < 1) {
    times.value = 1
  }
}
const isEnoughPoints = computed(() => {
  return myPoints.value >= spendPoints.value
})

const exchange = () => {
  if (!isEnoughPoints.value) {
    return
  }

  uni.showModal({
    title: "提示",
    content: `确定要兑换${times.value}次咨询吗？`,
    success: (res) => {
      if (res.confirm) {
        doExchange()
      }
    },
  })
}
const doExchange = () => {
  request({
    url: "/mini/point_exchange",
    method: "post",
    data: {
      times: times.value,
    },
  }).then((res) => {
    if (res.code === 200) {
      wx.showToast({
        title: "兑换成功",
        icon: "none",
      })
      store.getUserInfo()
      refresh()
    } else {
      // uni.showToast({
      //   title: res.message,
      //   icon: "none",
      // })
    }
  })
}

onLoad(() => {
  refresh()
})
onShow(() => {
  console.log("show")
})
onReachBottom(() => {
  loadMore()
})
onShareAppMessage((res) => {
  return {
    title: store.inviteShareTitle,
    imageUrl: store.inviteShareImage,
    path: `/pages/index/index?invitation_code=${store.userInfo.invitation_code}`,
  }
})
</script>

<template>
  <navBar title="我的积分" />
  <view class="gradient-bg"></view>
  <image
    class="header-bg"
    src="/static/images/mypoints-bg.png"
    mode="widthFix"
  />
  <view class="page">
    <view class="points-card">
      <view class="tips">
        <image src="/static/images/coin.png" />
        当前可用积分
      </view>
      <view class="points">{{ myPoints }}</view>
      <view class="invite">
        <view class="invite-text">
          <view class="invite-text-title">每邀请1位好友注册</view>
          <view class="invite-text-points">
            <text>
              奖励
              {{ store.inviteEachUserRewardPoints }}
              积分~
            </text>
          </view>
        </view>
        <button class="invite-btn" openType="share">立即邀请</button>
      </view>
    </view>
    <view class="exchange-card">
      <view class="title">积分兑换咨询次数</view>
      <view class="tips">{{ timeToPointsRate }}积分可兑换一次</view>
      <view class="step">
        <view class="minus" @click="addTimes(-1)">
          <image src="/static/images/minus.svg" />
        </view>
        <view class="content">
          <view class="times">{{ times }}次</view>
          <view class="points">消耗{{ spendPoints }}积分</view>
        </view>
        <view class="plus" @click="addTimes(1)">
          <image src="/static/images/plus.svg" />
        </view>
      </view>
      <view class="btn-box">
        <button class="btn" :disabled="!isEnoughPoints" @click="exchange">
          {{ isEnoughPoints ? "立即兑换" : "积分不足" }}
        </button>
      </view>
    </view>
    <view class="history-card">
      <view class="history-card-title">积分记录</view>
      <view class="history-card-list">
        <view
          class="history-card-item"
          v-for="(item, index) in list"
          :key="index"
        >
          <view class="history-card-item-left">
            <text class="title">{{ item.remark }}</text>
            <text class="time">{{ item.created_at }}</text>
          </view>
          <view class="history-card-item-right">
            <text
              :class="[
                'points',
                item.point > 0 ? 'points-black' : 'points-gray',
              ]"
              >{{ item.point }}</text
            >
          </view>
        </view>

        <loadMoreText :status="loadMoreStatus" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.header-bg {
  width: 100%;
  height: 380rpx;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

.page {
  padding: 40rpx;
  padding-top: calc(42rpx + v-bind(headerHeight));
}

.points-card {
  height: 392rpx;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );
  padding: 40rpx;
  border-radius: 48rpx 48rpx 48rpx 48rpx;
  box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;

  .tips {
    height: 40rpx;
    line-height: 40rpx;
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #6e7191;

    image {
      width: 56rpx;
      height: 56rpx;
      vertical-align: middle;
    }
  }

  .points {
    font-size: 88rpx;
    color: #253a57;
    margin-top: 12rpx;
  }

  .invite {
    height: 148rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12rpx;

    background: rgba(70, 111, 255, 0.04);
    border-radius: 48rpx 48rpx 48rpx 48rpx;
    padding: 26rpx 34rpx 28rpx 44rpx;

    .invite-text {
      display: flex;
      flex-direction: column;
    }

    .invite-text-title {
      font-size: 28rpx;
      color: #333;
    }

    .invite-text-points {
      font-weight: bold;
      font-size: 32rpx;
      color: #253a57;
      position: relative;
      width: fit-content;
      line-height: 1;
      margin-top: 8rpx;

      &::before {
        content: "";
        width: 100%;
        height: 12rpx;
        background: linear-gradient(
          90deg,
          #466fff 0%,
          rgba(70, 111, 255, 0) 100%
        );
        border-radius: 6rpx 6rpx 6rpx 6rpx;
        position: absolute;
        bottom: -2rpx;
        left: 0;
        z-index: 0;
      }

      text {
        position: relative;
        z-index: 1;
      }
    }

    .invite-btn {
      width: 170rpx;
      height: 80rpx;
      line-height: 80rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #fff;
      background: #466fff;
      border-radius: 24rpx 24rpx 24rpx 24rpx;
    }
  }
}

.exchange-card {
  background-color: #fff;
  padding: 40rpx;
  border-radius: 48rpx 48rpx 48rpx 48rpx;
  margin-top: 20rpx;

  .title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .tips {
    font-size: 24rpx;
    color: #666;
    margin-bottom: 20rpx;
  }

  .step {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    border-radius: 10rpx;

    .minus,
    .plus {
      width: 80rpx;
      height: 80rpx;
      border-radius: 24rpx 24rpx 24rpx 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      image {
        width: 40rpx;
        height: 40rpx;
      }
    }

    .minus {
      background: #f1f1f1;
    }

    .plus {
      background: rgba(70, 111, 255, 0.06);
    }

    .content {
      text-align: center;

      .times {
        font-size: 32rpx;
        color: #253a57;
        font-weight: bold;
      }

      .points {
        font-size: 24rpx;
        color: #6e7191;
        margin-top: 6rpx;
      }
    }
  }

  .btn-box {
    display: flex;
    justify-content: center;
    margin-top: 20rpx;

    .btn {
      width: 170rpx;
      height: 80rpx;
      line-height: 80rpx;
      background: #466fff;
      color: #fff;
      font-size: 28rpx;
      border-radius: 24rpx;

      &[disabled] {
        background: rgba(70, 111, 255, 0.3);
      }
    }
  }
}

.history-card {
  background-color: #fff;
  padding: 40rpx;
  border-radius: 48rpx 48rpx 48rpx 48rpx;
  margin-top: 20rpx;

  .history-card-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .history-card-list {
    .history-card-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx;
      border-bottom: 1px solid #f4f5fa;

      .history-card-item-left {
        display: flex;
        flex-direction: column;

        .title {
          font-size: 32rpx;
          color: #333;
          margin-bottom: 5rpx;
        }

        .time {
          font-size: 24rpx;
          color: rgba(110, 113, 145, 1);
        }
      }

      .history-card-item-right {
        .points {
          font-size: 32rpx;
          color: #333;
        }

        .points-black {
          font-weight: bold;
          font-size: 32rpx;
          color: #253a57;
        }

        .points-gray {
          font-weight: bold;
          font-size: 32rpx;
          color: rgba(171, 176, 186, 1);
        }
      }
    }
  }
}
</style>
