<script setup>
import { ref } from "vue"
const loadMoreStatus = ref("loadMore") // loadMore, loading, noMore
</script>

<template>
  <navBar title="系统通知" />
  <view class="gradient-bg"></view>
  <view class="page">
    <view class="list">
      <view class="item new">
        前苏联科学分类学家凯达洛夫院士把心理学定位于他们所绘制的科学三角形的中心而三角形的三个顶角分别是自然科学。
      </view>
      <loadMoreText :status="loadMoreStatus" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  padding-top: 200rpx;
}

.list {
  padding: 40rpx;

  .item {
    padding: 40rpx;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
    border-radius: 48rpx 48rpx 48rpx 48rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.5);

    font-size: 28rpx;
    color: #14142b;
    position: relative;

    &.new::before {
      content: "";
      display: block;
      width: 12rpx;
      height: 12rpx;
      background: #ff4141;
      border-radius: 50%;
      position: absolute;
      left: 24rpx;
      top: 24rpx;
    }
  }
}
</style>
