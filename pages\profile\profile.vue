<script setup>
import { ref, computed } from "vue"
import { onLoad, onShow } from "@dcloudio/uni-app"
import { useStore } from "/store"
import request, { fetchUpload } from "/request"

const store = useStore()

const formData = ref({
  avatar_id: "",
  name: "",
  phone: "",
  province_code: "",
  city_code: "",
  district_code: "",
  industry_code: "",
})

const submit = () => {
  if (!formData.value.name) {
    uni.showToast({
      title: "请输入昵称",
      icon: "none",
    })
    return
  }
  if (!formData.value.phone) {
    uni.showToast({
      title: "请获取手机号码",
      icon: "none",
    })
    return
  }
  if (!formData.value.province_code) {
    uni.showToast({
      title: "请选择所在地区",
      icon: "none",
    })
    return
  }
  // if (!formData.value.industry_code) {
  //   uni.showToast({
  //     title: "请输入所属行业",
  //     icon: "none",
  //   })
  //   return
  // }

  request({
    url: "/mini/info",
    method: "post",
    data: formData.value,
  }).then((res) => {
    console.log(res)
    if (res.code === 200) {
      uni.showToast({
        title: "保存成功",
      })
      setTimeout(() => {
        store.getUserInfo()
        uni.navigateBack()
      }, 2000)
    } else {
      // uni.showToast({
      //   title: res.message,
      //   icon: "none",
      // })
    }
  })
}

const avatar = ref("")
const uploadAvatar = (e) => {
  fetchUpload(e.detail.avatarUrl).then((res) => {
    console.log(res)
    avatar.value = res.result.file.full_path
    formData.value.avatar_id = res.result.file.id
  })
}
const previewImage = () => {
  uni.previewImage({
    urls: [avatar.value],
  })
}

const getPhoneNumber = async (e) => {
  if (e.detail.errMsg === "getPhoneNumber:ok") {
    const res = await request({
      url: "/mini/get_phone",
      method: "post",
      data: {
        code: e.detail.code,
      },
    })
    if (res.code === 200) {
      formData.value.phone = res.result.purePhoneNumber
    }
  }
}

const regionText = ref("")
const regionChange = (e) => {
  const { province, city, district } = e
  formData.value.province_code = province.id
  formData.value.city_code = city.id
  formData.value.district_code = district.id
  regionText.value = `${province.name} ${city.name} ${district.name}`
}

const industryList = ref([])
const industryIndex = ref(null)
const industryText = computed(() => {
  return industryList.value[industryIndex.value]?.name || ""
})
const industryChange = (e) => {
  const { value } = e.detail
  industryIndex.value = value
  formData.value.industry_code = industryList.value[value].code
}

const handleNicknameBlur = (e) => {
  formData.value.name = e.target.value.trim()
}

onLoad(async () => {
  const userInfo = store.userInfo
  formData.value = {
    avatar_id: userInfo.avatar_id,
    name: userInfo.name,
    phone: userInfo.phone,
    province_code: userInfo.province_code,
    city_code: userInfo.city_code,
    district_code: userInfo.district_code,
    industry_code: userInfo.industry_code,
  }
  avatar.value = store.avatar
  if (userInfo.province_name) {
    regionText.value = `${userInfo.province_name} ${userInfo.city_name} ${userInfo.district_name}`
  }
  const res = await request({
    url: "/mini/industry",
    method: "get",
  })
  industryList.value = res.result || []
  if (userInfo.industry_code) {
    industryIndex.value = industryList.value.findIndex(
      (item) => item.code === userInfo.industry_code
    )
  }
})
onShow(() => {
  // store.getUserInfo()
})
</script>

<template>
  <navBar title="我的资料" />
  <view class="gradient-bg"></view>
  <view class="page">
    <view class="form">
      <view class="upload-avatar">
        <image
          @click="previewImage"
          :src="avatar"
          mode="aspectFill"
          class="avatar"
        />
        <button
          class="btn"
          open-type="chooseAvatar"
          @chooseavatar="uploadAvatar"
        >
          <image class="icon" src="/static/images/camera.png" />
        </button>
      </view>
      <view class="form-item">
        <view class="form-item-label">昵称</view>
        <view class="form-item-content">
          <input
            placeholder="输入昵称"
            v-model="formData.name"
            type="nickname"
            @blur="handleNicknameBlur"
          />
        </view>
      </view>
      <view class="form-item">
        <view class="form-item-label">手机号码</view>
        <view class="form-item-content">
          <input
            type="text"
            placeholder="获取手机号"
            disabled
            v-model="formData.phone"
          />

          <button
            class="get-phone-btn"
            open-type="getPhoneNumber"
            @getphonenumber="getPhoneNumber"
          >
            点击获取
          </button>
        </view>
      </view>
      <view class="form-item">
        <view class="form-item-label">所在地区</view>
        <view class="form-item-content">
          <regionPicker @change="regionChange">
            <input
              type="text"
              placeholder="选择所在地区"
              :value="regionText"
              disabled
            />
          </regionPicker>
        </view>
      </view>
      <!--
        <view class="form-item">
          <view class="form-item-label">所属行业</view>
          <view class="form-item-content">
            <picker
              mode="selector"
              :value="industryIndex"
              :range="industryList"
              range-key="name"
              @change="industryChange"
            >
              <input
                type="text"
                placeholder="选择所属行业"
                :value="industryText"
                disabled
              />
            </picker>
          </view>
        </view>
      -->
    </view>

    <view class="bottom-wrap">
      <button class="btn primary" @click="submit">保存</button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  padding-top: 200rpx;
}

.form {
  padding: 0 40rpx;
  padding-bottom: 270rpx;
  .upload-avatar {
    margin: 48px auto;
    width: 240rpx;
    height: 240rpx;
    position: relative;

    .avatar {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }

    .btn {
      position: absolute;
      bottom: 0;
      right: 0;

      width: 64rpx;
      height: 64rpx;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 255, 255, 0.7) 100%
      );
      box-shadow: 0rpx 32rpx 48rpx 0rpx rgba(160, 163, 189, 0.16);
      border-radius: 108rpx 108rpx 108rpx 108rpx;
      border: 2rpx solid rgba(252, 252, 252, 0.5);

      display: flex;
      justify-content: center;
      align-items: center;

      .icon {
        width: 33rpx;
        height: 30rpx;
      }
    }
  }
  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 40rpx;
    border-bottom: 1px solid #f5f5f5;
    .form-item-label {
      font-weight: 500;
      font-size: 32rpx;
      color: #4e4b66;
      margin-bottom: 16rpx;
    }
    .form-item-content {
      position: relative;

      input {
        height: 128rpx;
        line-height: 128rpx;
        padding: 0 40rpx;
        border-radius: 48rpx 48rpx 48rpx 48rpx;
        background-color: #fff;
      }

      .get-phone-btn {
        position: absolute;
        z-index: 2;
        right: 40rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 160rpx;
        height: 72rpx;
        line-height: 72rpx;
        background: #466fff;
        box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
        border-radius: 24rpx;
        color: #fff;
        font-size: 26rpx;
        text-align: center;
      }
    }
  }
}

.bottom-wrap {
  position: fixed;
  z-index: 9;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 24rpx;
  background: rgba(248, 250, 254, 0.8);
  box-shadow: 0rpx 32rpx 48rpx 0rpx rgba(160, 163, 189, 0.16);
  border-radius: 64rpx 64rpx 0rpx 0rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);

  .btn {
    width: 100%;
  }
}
</style>
