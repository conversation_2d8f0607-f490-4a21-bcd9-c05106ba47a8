<script setup>
import { ref, computed } from "vue"
import request from "/request"
import { onLoad, onShow, onReachBottom } from "@dcloudio/uni-app"
import { useStore } from "@/store"

const store = useStore()

const packageList = ref([])
const activePackageIndex = ref(0)

const rechargePopup = ref(null)
const openPopup = () => {
  rechargePopup.value.open()
}
const closePopup = () => {
  console.log("close")
  rechargePopup.value.close()
}

const loadPackageList = async () => {
  const res = await request({
    url: "/mini/package",
    method: "get",
    data: {
      size: 99,
    },
  })
  packageList.value = res.result?.data || []
}

const page = ref(1)
const pageSize = 10
const loadMoreStatus = ref("loadMore") // loadMore, loading, noMore
const rechargeList = ref([])
const loadRechargeList = async () => {
  loadMoreStatus.value = "loading"
  const res = await request({
    url: "/mini/order",
    method: "get",
    data: {
      page: page.value,
      size: pageSize,
    },
  })
  const list = res.result?.data.map((item) => {
    return {
      ...item.snapshot,
      paid_at: item.paid_at,
    }
  })
  rechargeList.value = rechargeList.value.concat(list)
  if (res.result?.data?.length < pageSize) {
    loadMoreStatus.value = "noMore"
  } else {
    loadMoreStatus.value = "loadMore"
  }
  page.value++
}
const loadMore = () => {
  if (loadMoreStatus.value !== "loadMore") return
  loadRechargeList()
}
const refresh = () => {
  page.value = 1
  rechargeList.value = []
  loadRechargeList()
}

const onRechargeSuccess = () => {
  refresh()
  rechargePopup.value.close()
}

onLoad(() => {
  loadPackageList()
  loadRechargeList()
})
onShow(() => {
  console.log("show")
})
onReachBottom(() => {
  loadMore()
})
</script>

<template>
  <navBar title="充值" />
  <view class="gradient-bg"></view>
  <image
    class="header-bg"
    src="/static/images/recharge-bg.png"
    mode="widthFix"
  />
  <view class="page">
    <view class="info">
      <view class="limit-label">当前剩余咨询次数</view>
      <view class="limit-value">
        <text class="value">{{ store.leftTimes }}</text>
        <text class="unit">次</text>
      </view>
      <button class="btn" @click="openPopup">我要充值</button>
    </view>
    <view class="recharge-list">
      <view class="title">充值明细</view>
      <view class="recharge-item" v-for="item in rechargeList" :key="item.id">
        <view class="recharge-item-left">
          <view class="recharge-item-title">{{ item.name }}</view>
          <view class="recharge-item-date">{{ item.paid_at }}</view>
        </view>
        <view class="recharge-item-right">￥{{ item.price }}</view>
      </view>
      <loadMoreText :status="loadMoreStatus" />
    </view>
  </view>

  <uni-popup ref="rechargePopup" type="bottom" :safe-area="false">
    <view class="popup">
      <view class="popup-close" @click="closePopup">
        <image src="/static/images/close.svg" />
      </view>
      <view class="popup-title">充值</view>
      <view class="popup-content">
        <rechargeBox
          :packageList="packageList"
          v-model="activePackageIndex"
          @success="onRechargeSuccess"
        />
      </view>
    </view>
  </uni-popup>
</template>

<style lang="scss" scoped>
.header-bg {
  width: 100%;
  height: 380rpx;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

.page {
  padding: 40rpx;
  padding-top: 190rpx;
}

.info {
  padding: 40rpx;
  height: 392rpx;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );
  box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
  border-radius: 48rpx 48rpx 48rpx 48rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;

  .limit-label {
    font-size: 24rpx;
    color: #6e7191;
    text-align: center;
  }

  .limit-value {
    font-size: 48rpx;
    color: #362c26;
    text-align: center;
    margin-top: 18rpx;
    display: flex;
    align-items: baseline;
    gap: 8rpx;

    .value {
      font-size: 88rpx;
      font-weight: bold;
    }

    .unit {
      font-size: 36rpx;
      font-weight: 500;
    }
  }

  .btn {
    margin-top: 42rpx;
    width: 290rpx;
    height: 96rpx;
    line-height: 96rpx;
    background: linear-gradient(270deg, #ddbd90 0%, #e9c49c 100%);
    box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
    border-radius: 36rpx 36rpx 36rpx 36rpx;
    font-size: 36rpx;
    color: #362c26;
    font-weight: 500;
  }
}

.recharge-list {
  padding: 40rpx;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%
  );
  border-radius: 48rpx 48rpx 48rpx 48rpx;
  margin-top: 24rpx;
  box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
  border: 2rpx solid rgba(255, 255, 255, 0.5);

  .title {
    font-weight: bold;
    font-size: 32rpx;
    color: #253a57;
    margin-bottom: 20rpx;
  }

  .recharge-item {
    width: 590rpx;
    height: 164rpx;
    background: #ffffff;
    border-radius: 48rpx 48rpx 48rpx 48rpx;
    box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.06);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx;
    margin-bottom: 24rpx;

    .recharge-item-left {
      .recharge-item-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
      }

      .recharge-item-date {
        font-size: 24rpx;
        color: #6e7191;
      }
    }

    .recharge-item-right {
      font-weight: bold;
      font-size: 32rpx;
      color: #253a57;
    }
  }
}

.popup {
  background: rgba(255, 255, 255, 0.88);
  border-radius: 64rpx 64rpx 0rpx 0rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  padding: 40rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  backdrop-filter: blur(16rpx);

  .popup-close {
    width: 48rpx;
    height: 48rpx;
    position: absolute;
    top: 40rpx;
    right: 40rpx;

    image {
      width: 48rpx;
      height: 48rpx;
    }
  }

  .popup-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #253a57;
    text-align: center;
  }

  .popup-content {
    margin-top: 40rpx;
  }
}
</style>
