import { defineStore } from "pinia"
import request from "/request"

export const useStore = defineStore("store", {
  state: () => ({
    // baseUrl: "https://x.yuexianfawu.com",
    // socketUrl: "wss://x.yuexianfawu.com/chat",
    baseUrl: "http://10.0.0.7:20010",
    socketUrl: "ws://10.0.0.7:8008",
    token: "",
    userInfo: {},
    config: {},
    headerHeight: "0",
    sessionId: "",

    nlsToken: "",
    nlsAppKey: "UGyxv85EdtQTT21E",
    nlsUrl: "wss://nls-gateway-cn-shanghai.aliyuncs.com/ws/v1",
  }),
  actions: {
    setSessionId(sessionId) {
      uni.setStorageSync("sessionId", sessionId)
      this.sessionId = sessionId
    },
    setToken(token) {
      uni.setStorageSync("token", token)
      this.token = token
    },
    setUserInfo(userInfo) {
      this.userInfo = userInfo
    },
    setConfig(config) {
      this.config = config
    },
    setHeaderHeight(headerHeight) {
      this.headerHeight = headerHeight
    },
    login(invitationCode) {
      return new Promise((resolve, reject) => {
        uni.login({
          success: (res) => {
            request({
              url: "/mini/login",
              method: "POST",
              data: {
                code: res.code,
                invited_by_code: invitationCode,
              },
            }).then((res) => {
              this.setToken(res.result.token)
              this.setUserInfo(res.result.user)
              resolve(res)
            })
          },
          fail: (err) => {
            reject(err)
          },
        })
      })
    },
    logOut() {
      this.setToken("")
      this.setUserInfo({})
      uni.removeStorageSync("token")
    },
    getUserInfo() {
      return new Promise((resolve, reject) => {
        request({
          url: "/mini/info",
        }).then((res) => {
          this.setUserInfo(res.result)
          resolve(res)
        })
      })
    },
    getConfig() {
      return new Promise((resolve, reject) => {
        request({
          url: "/mini/config",
        }).then((res) => {
          this.setConfig(res.result)
          resolve(res)
        })
      })
    },
    minusLeftTimes() {
      this.userInfo.left_times--
    },
    fetchNlsToken() {
      return new Promise((resolve, reject) => {
        request({
          url: "/mini/tts_token",
        })
          .then((res) => {
            this.nlsToken = res.result?.Id || ""
            resolve(res)
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
  },
  getters: {
    isLogin: (state) => !!state.token,
    isCompleteUserInfo: (state) => !!state.userInfo?.phone,
    leftTimes: (state) => state.userInfo?.left_times || 0,
    accountAvailability(state) {
      return state.isLogin && this.isCompleteUserInfo && this.leftTimes > 0
    },
    points: (state) => state.userInfo?.points || 0,
    avatar: (state) =>
      state.userInfo?.avatar?.full_path || "/static/images/default-avatar.png",
    serviceArticleId: (state) => state.config?.service_id || 0,
    agreementArticleId: (state) => state.config?.user_service_id || 0,
    privacyArticleId: (state) => state.config?.privacy_policy_id || 0,
    timeToPointsRate: (state) => state.config?.each_times_need_point || 0,
    registerGiveTimes: (state) => state.config?.register_give_times || 0,
    inviteEachUserRewardPoints: (state) =>
      state.config?.invite_each_user_reward_point || 0,
    inviteShareImage: (state) =>
      state.config?.invite_share_image?.full_path || "",
    inviteShareTitle: (state) =>
      state.config?.invite_share_title || "邀请好友注册，赚取积分",
    paySwitch: (state) => state.config?.pay_switch || false,
  },
})
