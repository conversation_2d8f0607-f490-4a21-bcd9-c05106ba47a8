<template>
  <view class="typewriter" :style="textStyle">
    <view class="text">
      <mp-html
        :content="md.render(this.currentText)"
        :tag-style="{
          p: 'margin-bottom: 40rpx;',
          li: 'margin-bottom: 40rpx;',
        }"
      />
    </view>
  </view>
</template>

<script>
import markdownit from "markdown-it"
const md = markdownit()

export default {
  props: {
    text: {
      type: String,
      default: "",
    },
    cursorImg: {
      type: String,
      default: "", // 光标图片的默认 URL
    },
    speed: {
      type: Number,
      default: 100, // 打字速度，单位：毫秒
    },
    textStyle: {
      type: Object,
      default: () => ({}), // 文字样式对象
    },
  },
  watch: {
    text(newText) {
      this.startTyping()
    },
  },
  data() {
    return {
      currentText: "", // 当前显示的文字
      currentIndex: 0, // 当前文字的索引
      typingFinished: false, // 打字完成的标志
      md,
    }
  },
  mounted() {
    this.startTyping()
  },
  methods: {
    startTyping() {
      if (this.text) {
        const textLength = this.text.length
        const typingInterval = setInterval(() => {
          // console.log(this.currentText)
          if (this.currentIndex < textLength) {
            this.currentText += this.text[this.currentIndex]
            this.currentIndex++
          } else {
            clearInterval(typingInterval)
            this.typingFinished = true
            this.$emit("finished", "")
          }
        }, this.speed)
      }
    },
  },
}
</script>

<style scoped></style>
