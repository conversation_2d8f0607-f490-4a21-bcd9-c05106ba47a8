 # 欢迎使用 jp-typewriter 打字组件

### 安装方式
本组件符合[easycom](https://uniapp.dcloud.io/collocation/pages?id=easycom)规范，`HBuilderX 2.5.5`起，只需将本组件导入项目，在页面`template`中即可直接使用，无需在页面中`import`和注册`components`。

##有项目需要开发的请联系 QQ:371524845
###开发不易，如果帮助到你的，请支持 有问题请留言，作者会积极更新

###项目实例请查看 @/jp-layout/pages/index/index.vue

当你使用这个打字机效果的组件时，可以按照以下步骤进行操作：
引入组件：在需要使用打字机效果的页面或组件中，引入该打字机组件。例如，假设组件文件名为
```html
<template>
  <div>
    <jp-typewriter :text="text" :cursorImg="cursorImg" :speed="speed" :textStyle="textStyle" @finished="typingFinished"></jp-typewriter>
  </div>
</template>

<script>
export default {
  data() {
    return {
      text: 'Hello, World!', // 要显示的文字内容
      cursorImg: 'https://example.com/cursor.png', // 光标图片的 URL
      speed: 100, // 打字速度，单位：毫秒
      textStyle: {
        color: 'red',
        fontWeight: 'bold',
        fontSize: '16px',
      }, // 文字样式对象
    };
  },
  methods: {
    typingFinished() {
      // 打字完成后的回调函数
      console.log('Typing finished');
    },
  },
};
</script>
```

自定义属性：根据需要，修改组件中的属性来控制打字机效果的展示。
text：要显示的文字内容。
cursorImg：光标图片的 URL，如果不需要光标图片，可以将其设为空字符串。
speed：打字速度，单位为毫秒。
textStyle：文字样式对象，可以设置字体颜色、字体大小、字体粗细等样式。
事件监听：通过监听 @finished 事件，可以在打字完成后执行特定的操作。



